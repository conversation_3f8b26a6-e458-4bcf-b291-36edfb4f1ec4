using Bootis.Identity.Domain.Entities;

namespace Bootis.Identity.Application.Authentication.Models;

public class CreateTemporaryTokenResponse
{
    public Guid TemporaryToken { get; set; }
    public DateTimeOffset ExpiresInMinutes { get; set; }
    public DateTimeOffset CreatedAt { get; set; }


    public static implicit operator CreateTemporaryTokenResponse(TemporaryLogin temporaryLogin)
    {
        return new CreateTemporaryTokenResponse
        {
            CreatedAt = temporaryLogin.CreatedAt,
            ExpiresInMinutes = temporaryLogin.ExpiresAt,
            TemporaryToken = temporaryLogin.Id
        };
    }
}