<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Shared\Bootis.Shared.Application\Bootis.Shared.Application.csproj"/>
        <ProjectReference Include="..\Bootis.Identity.Domain\Bootis.Identity.Domain.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3"/>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.14.0" />
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>
