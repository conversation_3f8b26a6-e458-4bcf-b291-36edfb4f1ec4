using Bootis.Catalogo.Domain.AggregatesModel.MensagemAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoMensagemAggregate;

public class ProdutoMensagem : Entity, IAggregateRoot
{
    public ProdutoMensagem()
    {
    }

    public ProdutoMensagem(Mensagem mensagem,
        Produto produto)
    {
        Mensagem = mensagem;
        Produto = produto;
    }

    public Guid ProdutoId { get; set; }
    public Guid MensagemId { get; set; }

    #region Navigation properties

    public virtual Produto Produto { get; set; }
    public virtual Mensagem Mensagem { get; set; }

    #endregion
}