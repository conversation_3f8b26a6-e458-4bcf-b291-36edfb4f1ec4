using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoFormaFarmaceuticaAggregate;

public class EmbalagemClassificacaoFormaFarmaceutica : Entity, IAggregateRoot, ITenant
{
    public EmbalagemClassificacaoFormaFarmaceutica()
    {
    }

    public EmbalagemClassificacaoFormaFarmaceutica(EmbalagemClassificacao embalagemClassificacao,
        FormaFarmaceutica formaFarmaceutica)
    {
        EmbalagemClassificacao = embalagemClassificacao;
        FormaFarmaceutica = formaFarmaceutica;
    }

    public Guid FormaFarmaceuticaId { get; set; }
    public Guid EmbalagemClassificacaoId { get; set; }

    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    #region Navigation properties

    public virtual FormaFarmaceutica FormaFarmaceutica { get; set; }
    public virtual EmbalagemClassificacao EmbalagemClassificacao { get; set; }

    #endregion
}