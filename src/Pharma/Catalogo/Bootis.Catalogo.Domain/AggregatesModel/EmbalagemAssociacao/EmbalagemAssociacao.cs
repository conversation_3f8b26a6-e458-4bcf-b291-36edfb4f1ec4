using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.EmbalagemAssociacao;

public class EmbalagemAssociacao : Entity
{
    public EmbalagemAssociacao()
    {
    }

    public EmbalagemAssociacao(ProdutoEmbalagem produtoEmbalagem,
        ProdutoEmbalagem produtoEmbalagemAssociada,
        int quantidadeEmbalagem) : this()
    {
        Initialize(produtoEmbalagem, produtoEmbalagemAssociada);
        QuantidadeEmbalagem = quantidadeEmbalagem;
    }

    public Guid? ProdutoEmbalagemId { get; set; }
    public Guid? ProdutoEmbalagemAssociadaId { get; private set; }
    public int? QuantidadeEmbalagem { get; private set; }

    private void Initialize(ProdutoEmbalagem produtoEmbalagem,
        ProdutoEmbalagem produtoEmbalagemAssociada)
    {
        ProdutoEmbalagem = produtoEmbalagem;
        ProdutoEmbalagemAssociada = produtoEmbalagemAssociada;
    }

    public void AtualizarEmbalagemAssociacao(int quantidade)
    {
        QuantidadeEmbalagem = quantidade;
    }

    #region Navigation properties

    public virtual ProdutoEmbalagem ProdutoEmbalagem { get; set; }
    public virtual ProdutoEmbalagem ProdutoEmbalagemAssociada { get; set; }

    #endregion
}