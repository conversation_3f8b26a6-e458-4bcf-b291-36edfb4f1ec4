using Bootis.Catalogo.Domain.AggregatesModel.ModeloOrdemManipulacaoAggregate;
using Bootis.Catalogo.Domain.Dtos.FormaFarmaceutica;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;

public class FormaFarmaceutica() : Entity, IAggregateRoot, ITenant
{
    public FormaFarmaceutica(IFormaFarmaceuticaDto formaFarmaceuticaDto,
        Guid laboratorioId, Guid? modeloOrdemManipulacaoId) : this()
    {
        Descricao = formaFarmaceuticaDto.Descricao;
        Ordem = formaFarmaceuticaDto.Ordem;
        PercentualMinimoExcipiente = formaFarmaceuticaDto.PercentualMinimoExcipiente;
        LaboratorioId = laboratorioId;
        TipoCalculo = formaFarmaceuticaDto.TipoCalculo;
        UsoFormaFarmaceutica = formaFarmaceuticaDto.UsoFormaFarmaceutica;
        UnidadeMedidaId = formaFarmaceuticaDto.UnidadeMedidaId;
        Apresentacao = formaFarmaceuticaDto.Apresentacao;
        ValidadeDias = formaFarmaceuticaDto.ValidadeDias;
        CustoOperacional = formaFarmaceuticaDto.CustoOperacional;
        ModeloOrdemManipulacaoId = modeloOrdemManipulacaoId;
        DataCadastro = DateTime.UtcNow;
        Ativo = true;
    }

    public Guid LaboratorioId { get; private set; }
    public string Descricao { get; private set; }
    public bool Ativo { get; private set; }
    public int Ordem { get; private set; }
    public decimal PercentualMinimoExcipiente { get; private set; }
    public TipoCalculo TipoCalculo { get; private set; }
    public UsoFormaFarmaceutica UsoFormaFarmaceutica { get; private set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; private set; }
    public string Apresentacao { get; private set; }
    public int ValidadeDias { get; private set; }
    public DateTime DataCadastro { get; private set; }
    public decimal CustoOperacional { get; private set; }
    public Guid? ModeloOrdemManipulacaoId { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void AtualizarFormaFarmaceutica(IFormaFarmaceuticaDto formaFarmaceuticaDto, Guid laboratorioId, Guid? modeloOrdemManipulacaoId)
    {
        ValidarUnidadeMedida(formaFarmaceuticaDto.UnidadeMedidaId);

        Descricao = formaFarmaceuticaDto.Descricao;
        TipoCalculo = formaFarmaceuticaDto.TipoCalculo;
        Ordem = formaFarmaceuticaDto.Ordem;
        PercentualMinimoExcipiente = formaFarmaceuticaDto.PercentualMinimoExcipiente;
        LaboratorioId = laboratorioId;
        UsoFormaFarmaceutica = formaFarmaceuticaDto.UsoFormaFarmaceutica;
        Apresentacao = formaFarmaceuticaDto.Apresentacao;
        ValidadeDias = formaFarmaceuticaDto.ValidadeDias;
        UnidadeMedidaId = formaFarmaceuticaDto.UnidadeMedidaId;
        CustoOperacional = formaFarmaceuticaDto.CustoOperacional;
        ModeloOrdemManipulacaoId = modeloOrdemManipulacaoId;
    }

    public void AtualizarStatus(bool ativo)
    {
        Ativo = ativo;
    }

    private static void ValidarUnidadeMedida(UnidadeMedidaAbreviacao unidadeMedidaId)
    {
        UnidadeMedidaAbreviacao[] unidadePosologia =
            { UnidadeMedidaAbreviacao.un, UnidadeMedidaAbreviacao.mL, UnidadeMedidaAbreviacao.L };

        if (!unidadePosologia.Contains(unidadeMedidaId))
            throw new ValidationException(
                Localizer.Instance.GetMessage_FormaFarmaceutica_UnidadeMedidaInvalida(unidadeMedidaId.ToString()));
    }

    #region Navigation Properties
    public virtual ModeloOrdemManipulacao ModeloOrdemManipulacao { get; set; }
    #endregion
}