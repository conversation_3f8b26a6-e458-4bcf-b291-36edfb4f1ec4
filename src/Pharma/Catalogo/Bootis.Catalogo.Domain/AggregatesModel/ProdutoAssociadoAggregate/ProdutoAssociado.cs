using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoAssociadoAggregate;

public class ProdutoAssociado : Entity, ITenant, IAggregateRoot
{
    public ProdutoAssociado()
    {
    }

    public ProdutoAssociado(Produto produto,
        Guid produtoAssociadoId,
        Guid formaFarmaceuticaId,
        decimal dosagemMinima,
        decimal dosagemMaxima,
        UnidadeMedidaAbreviacao unidadeMedidaDosagem,
        decimal quantidadeAssociada,
        UnidadeMedidaAbreviacao unidadeMedidaQuantidadeAssociada,
        TipoRelacaoQuantidade tipoRelacaoQuantidade,
        bool acumula)
    {
        Produto = produto;
        ProdutoAssociadoId = produtoAssociadoId;
        FormaFarmaceuticaId = formaFarmaceuticaId;
        DosagemMinima = dosagemMinima;
        DosagemMaxima = dosagemMaxima;
        UnidadeMedidaDosagem = unidadeMedidaDosagem;
        QuantidadeAssociada = quantidadeAssociada;
        UnidadeMedidaQuantidadeAssociada = unidadeMedidaQuantidadeAssociada;
        TipoRelacaoQuantidade = tipoRelacaoQuantidade;
        Acumula = acumula;
    }

    public Guid ProdutoId { get; set; }
    public Guid ProdutoAssociadoId { get; private set; }
    public Guid? FormaFarmaceuticaId { get; private set; }
    public decimal? DosagemMinima { get; private set; }
    public decimal? DosagemMaxima { get; private set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaDosagem { get; private set; }
    public decimal QuantidadeAssociada { get; private set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaQuantidadeAssociada { get; private set; }
    public TipoRelacaoQuantidade TipoRelacaoQuantidade { get; private set; }
    public bool Acumula { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void Atualizar(Produto produtoAssociado, FormaFarmaceutica formaFarmaceutica,
        decimal dosagemMinima, decimal dosagemMaxima, UnidadeMedidaAbreviacao unidadeMedidaDosagem,
        decimal quantidadeAssociada, UnidadeMedidaAbreviacao unidadeMedidaQuantidadeAssociada,
        TipoRelacaoQuantidade tipoRelacaoQuantidade, bool acumula)
    {
        ProdutoAssociacao = produtoAssociado;
        FormaFarmaceutica = formaFarmaceutica;
        DosagemMinima = dosagemMinima;
        DosagemMaxima = dosagemMaxima;
        UnidadeMedidaDosagem = unidadeMedidaDosagem;
        QuantidadeAssociada = quantidadeAssociada;
        UnidadeMedidaQuantidadeAssociada = unidadeMedidaQuantidadeAssociada;
        TipoRelacaoQuantidade = tipoRelacaoQuantidade;
        Acumula = acumula;

        if (formaFarmaceutica == null) FormaFarmaceuticaId = null;

        if (DosagemMaxima < DosagemMinima)
            throw new DomainException(Localizer.Instance.GetMessage_Produto_DosagemInvalida());
    }

    #region Navigation properties

    public virtual Produto Produto { get; set; }
    public virtual Produto ProdutoAssociacao { get; set; }
    public virtual FormaFarmaceutica FormaFarmaceutica { get; set; }

    #endregion
}