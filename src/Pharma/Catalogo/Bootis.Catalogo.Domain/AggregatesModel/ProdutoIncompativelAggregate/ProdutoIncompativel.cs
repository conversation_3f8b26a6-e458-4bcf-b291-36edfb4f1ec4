using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoIncompativelAggregate;

public class ProdutoIncompativel : Entity, ITenant, IAggregateRoot
{
    public ProdutoIncompativel()
    {
    }

    public ProdutoIncompativel(Produto produto,
        Guid produtoIncompativelId,
        string descricao,
        NivelIncompatibilidade nivelIncompatibilidade)
    {
        Produto = produto;
        ProdutoIncompativelId = produtoIncompativelId;
        Descricao = descricao;
        NivelIncompatibilidade = nivelIncompatibilidade;
    }

    public Guid ProdutoId { get; set; }
    public Guid ProdutoIncompativelId { get; private set; }
    public string Descricao { get; private set; }
    public NivelIncompatibilidade NivelIncompatibilidade { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void Atualizar(Produto produtoIncompativel, string descricao,
        NivelIncompatibilidade nivelIncompatibilidade)
    {
        ProdutoIncompatibilidade = produtoIncompativel;
        Descricao = descricao;
        NivelIncompatibilidade = nivelIncompatibilidade;
    }

    #region Navigation properties

    public virtual Produto Produto { get; set; }
    public virtual Produto ProdutoIncompatibilidade { get; set; }

    #endregion
}