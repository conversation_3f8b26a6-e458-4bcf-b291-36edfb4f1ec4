using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.Mensagem.Cadastrar;
using Bootis.Catalogo.Domain.AggregatesModel.MensagemAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoMensagemAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.Mensagem;

public class CadastrarVinculadoProdutoRequestHandler(
    IUnitOfWork unitOfWork)
    : IRequestHandler<CadastrarVinculadoProdutoRequest>
{
    public async Task Handle(CadastrarVinculadoProdutoRequest request, CancellationToken cancellationToken)
    {
        var mensagemRepository =
            unitOfWork.GetRepository<IMensagemRepository>();
        var produtoRepository =
            unitOfWork.GetRepository<IProdutoRepository>();

        await mensagemRepository.ValidarMensagemPorDescricaoAsync(request.Descricao);
                
        var mensagem = new Domain.AggregatesModel.MensagemAggregate.Mensagem(request.Descricao,
            request.ExibeVenda,
            request.ExibeRotulagem,
            request.ExibeFichaPesagem,
            request.ExibeImpressaoRotulo);

        mensagemRepository.Add(mensagem);

        //TODO: Vinculo de Produto Mensagem não está funcionando.
        //var produtoMensagem = new Domain.AggregatesModel.ProdutoMensagemAggregate.ProdutoMensagem(mensagem, produto);

        //produtoMensagemRepository.Add(produtoMensagem);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}