using System.ComponentModel;
using Bootis.Catalogo.Domain.Dtos.Produto;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoCapsulaPronta;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoEmbalagem;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoMateriaPrima;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Catalogo.Application.Requests.Produto.Atualizar;

public abstract class AtualizarRequest : IProdutoDto, IRequest
{
    public Guid Id { get; set; }
    public Guid SubGrupoId { get; set; }
    public TipoClassificacaoPsicotropicaMedicamento? TipoClassificacaoPsicotropicaMedicamentoId { get; set; }
    public TipoTarjaMedicamento? TipoTarjaMedicamentoId { get; set; }
    public Guid? FornecedorId { get; set; }
    public string Descricao { get; set; }
    public string DescricaoRotulo { get; set; }
    public TipoClasseProdutoAbreviacao ClasseProdutoId { get; set; }
    public UnidadeMedidaAbreviacao UnidadeEstoqueId { get; set; }
    public bool ControlaLote { get; set; }
    public bool UsoContinuo { get; set; }
    public bool Etiqueta { get; set; }
    public bool ControlaQualidade { get; set; }
    public string CodigoBarras { get; set; }
    public string LinkImagem { get; set; }
    public bool DesativarProjecaoEstoque { get; set; }
    public decimal EstoqueMinimo { get; set; }
    public decimal EstoqueMaximo { get; set; }
}

public class AtualizarMateriaPrimaRequest : AtualizarRequest, IProdutoMateriaPrimaDto
{
    public Guid? CasId { get; set; }
    public Guid? DcbId { get; set; }
    public Guid? ProdutoExcipienteEspecificoId { get; set; }
    public UnidadeMedidaAbreviacao? UnidadePrescricaoId { get; set; }
    public TipoComponente? TipoComponenteId { get; set; }

    [DefaultValue(false)] public bool SomenteLaboratorio { get; set; }

    [DefaultValue(false)] public bool IsPellets { get; set; }

    [DefaultValue(false)] public bool IsExcipiente { get; set; }

    [DefaultValue(false)] public bool IsQsp { get; set; }

    [DefaultValue(false)] public bool SomenteDiluido { get; set; }

    public int? DiasValidade { get; set; }

    [DefaultValue(false)] public bool ExigeCapsulaGastroresistente { get; set; }

    [DefaultValue(false)] public bool IsMonodroga { get; set; }

    public decimal? ToleranciaPesagemUp { get; set; }
    public decimal? ToleranciaPesagemDown { get; set; }
    public string ObservacaoRotuloArmazenagem { get; set; }

    [DefaultValue(1)] public decimal? PesoMolecularSal { get; set; }

    [DefaultValue(1)] public decimal? PesoMolecularBase { get; set; }

    public decimal? FatorEquivalencia { get; set; }
    public decimal? Valencia { get; set; }
    public TipoClasseBiofarmaceutica? BiofarmaceuticaId { get; set; }
    public ProdutoMateriaPrimaInformacaoTecnicaDto InformacaoTecnica { get; set; }
}

public class AtualizarEmbalagemRequest : AtualizarRequest
{
    public Guid? ClassificacaoEmbalagemId { get; set; }
    public decimal? Volume { get; set; }
    public IEnumerable<ProdutoEmbalagemAssociacaoDto> EmbalagemAssociacao { get; set; }
    public IEnumerable<ProdutoEmbalagemCapsulaTamanhoAssociacaoDto> NumeroCapsulaAssociacao { get; set; }
    public IEnumerable<ModeloRotuloEmbalagemAssociacaoDto> ModeloRotuloAssociacao { get; set; }
}

public class AtualizarTipoCapsulaRequest : AtualizarRequest
{
    public Guid? CapsulaCorId { get; set; }
    public Guid? CapsulaTamanhoId { get; set; }
    public Guid? TipoCapsulaId { get; set; }
}

public class AtualizarCapsulaProntaRequest : AtualizarRequest
{
    public Guid? CapsulaTamanhoId { get; set; }
    public IEnumerable<ProdutoCapsulaProntaMateriaPrimaAssociacaoDto> MateriaPrimaAssociacao { get; set; }
}

public class AtualizarBaseRequest : AtualizarRequest
{
}