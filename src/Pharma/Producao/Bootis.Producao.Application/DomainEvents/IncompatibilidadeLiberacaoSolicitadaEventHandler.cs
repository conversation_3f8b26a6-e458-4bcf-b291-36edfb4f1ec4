using Bootis.Producao.Application.Services.Interfaces;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.Events;
using Bootis.Shared.Application.Events;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.ValueObjects.Auth;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bootis.Producao.Application.DomainEvents;

public class IncompatibilidadeLiberacaoSolicitadaEventHandler(
    IReceitaManipuladaRepository receitaManipuladaRepository,
    IValidarIncompatibilidadeAppService validadorIncompatibilidade,
    IIdentityClient identityClient,
    IUnitOfWork unitOfWork,
    ILogger<IncompatibilidadeLiberacaoSolicitadaEventHandler> logger)
    : INotificationHandler<DomainEventNotification<IncompatibilidadeLiberacaoSolicitadaEvent>>, IScopedService
{
    public async Task Handle(DomainEventNotification<IncompatibilidadeLiberacaoSolicitadaEvent> notification, CancellationToken cancellationToken)
    {
        var domainEvent = notification.DomainEvent;
        
        logger.LogInformation("Processando liberação de incompatibilidade para receita {ReceitaId} pelo usuário {UsuarioId}", 
            domainEvent.ReceitaManipuladaId, domainEvent.UsuarioId);

        try
        {
            // 1. Validar token temporário
            await ValidarTokenTemporarioAsync(domainEvent.TokenUsoUnico, domainEvent.UsuarioId, cancellationToken);

            // 2. Obter receita
            var receita = await receitaManipuladaRepository.ObterReceitaManipuladaPorIdAsync(domainEvent.ReceitaManipuladaId)
                ?? throw new ValidationException($"Receita manipulada com ID {domainEvent.ReceitaManipuladaId} não encontrada.");

            // 3. Validar incompatibilidades
            var incompatibilidades = await validadorIncompatibilidade.ObterCenariosIncompatibilidadeAsync(domainEvent.ProdutoIds);
            
            if (incompatibilidades.NivelMaisRestritivo == Catalogo.Domain.Enumerations.NivelIncompatibilidade.Ok)
                throw new ValidationException("Não há incompatibilidades para liberar");

            // 4. Verificar se pode ser liberada (não é bloqueio)
            if (incompatibilidades.NivelMaisRestritivo == Catalogo.Domain.Enumerations.NivelIncompatibilidade.Bloqueio)
                throw new ValidationException("Incompatibilidades de bloqueio não podem ser liberadas. A manipulação deve ser cancelada.");

            // 5. Registrar liberação no histórico técnico
            var produtosEnvolvidos = incompatibilidades.Incompatibilidades.Select(i =>
                $"{i.ProdutoDescricao} x {i.ProdutoIncompativelDescricao}").ToArray();

            receita.RegistrarLiberacaoComSenha(domainEvent.UsuarioId, produtosEnvolvidos, domainEvent.MotivoLiberacao);

            // 6. Registrar incompatibilidades como liberadas por senha no histórico técnico
            receita.RegistrarIncompatibilidade(domainEvent.UsuarioId, Catalogo.Domain.Enumerations.NivelIncompatibilidade.LiberacaoSenha,
                $"Incompatibilidades liberadas via token: {domainEvent.TokenUsoUnico}", produtosEnvolvidos);

            // 6. Salvar alterações
            receitaManipuladaRepository.Update(receita);
            await unitOfWork.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Liberação de incompatibilidade processada com sucesso para receita {ReceitaId}", 
                domainEvent.ReceitaManipuladaId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao processar liberação de incompatibilidade para receita {ReceitaId}", 
                domainEvent.ReceitaManipuladaId);
            throw;
        }
    }

    private async Task ValidarTokenTemporarioAsync(string tokenUsoUnico, Guid usuarioId, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(tokenUsoUnico))
            throw new ValidationException("Token de uso único é obrigatório para liberação de incompatibilidades.");

        var validationResult = await identityClient.IntrospectAsync(tokenUsoUnico, IntrospectType.TemporaryToken, cancellationToken);

        if (validationResult.IsT1) // TokenValidationFailure
        {
            var failure = validationResult.AsT1;
            throw new ValidationException($"Token de uso único inválido: {failure.ErrorMessage}");
        }

        var success = validationResult.AsT0; // TokenValidationSuccess
        
        // Verificar se o token pertence ao usuário correto
        if (success.UserSession.UserIdentity.UserId != usuarioId)
            throw new ValidationException("Token de uso único não pertence ao usuário solicitante.");

        // Verificar se o token ainda está ativo
        if (!success.IsActive)
            throw new ValidationException("Token de uso único expirado ou já utilizado.");
    }
}
