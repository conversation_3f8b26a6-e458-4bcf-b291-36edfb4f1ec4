using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Common.ValueObjects.Responses;
using MediatR;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Atualizar;

public class SalvarReceitaManipuladaRequest : IRequest<CadastrarGlobalResponse>, IRequest<ReceitaManipuladaResultadoDto>
{
    public Guid Id { get; set; }
    public Guid PacienteId { get; set; }
    public Guid? PrescritorId { get; set; }
    public Guid FormaFarmaceuticaId { get; set; }
    public decimal QuantidadeReceita { get; set; }
    public int QuantidadeRepetir { get; set; }
    public decimal? QuantidadeDose { get; set; }
    public List<ReceitaManipuladaComponenteDto> Componentes { get; set; } = new();

    public Guid? PosologiaId { get; set; }
    public string Observacao { get; set; }
    public TipoDesconto? TipoDesconto { get; set; }
    public decimal? Desconto { get; set; }
    public decimal? PercentualDesconto { get; set; }

    public Guid? ProdutoCapsulaId { get; set; }
    public Guid? ProdutoEmbalagemId { get; set; }
    
    public bool UsoContinuo { get; set; } = false;
    public TipoCalculoUsoContinuo? TipoUsoContinuo { get; set; }
    public decimal? QuantidadeReSell { get; set; }
    public PeriodosPosologia? PeriodicidadeReSell { get; set; }

    public class ReceitaManipuladaComponenteDto
    {
        public Guid ProdutoId { get; set; }
        public Guid? ProdutoSinonimoId { get; set; }
        public string DescricaoRotulo { get; set; }
        public decimal Quantidade { get; set; }
        public int UnidadeMedidaId { get; set; }
        public TipoComponente TipoQuantificacao { get; set; }
        public int Ordem { get; set; }
        public bool OcultaRotulo { get; set; }
    }
}