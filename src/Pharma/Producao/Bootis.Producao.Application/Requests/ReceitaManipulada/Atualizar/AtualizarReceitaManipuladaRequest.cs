using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Domain.Enumerations;
using MediatR;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Atualizar;

public class AtualizarReceitaManipuladaRequest : IRequest<AtualizarReceitaManipuladaResponse>
{
    public Guid Id { get; set; }
    public Guid PacienteId { get; set; }
    public Guid FormaFarmaceuticaId { get; set; }
    public Guid? PrescritorId { get; set; }
    public DateOnly DataPrescricao { get; set; }
    public decimal QuantidadeReceita { get; set; }
    public int QuantidadeRepetir { get; set; }
    public decimal? QuantidadeDose { get; set; }
    public IEnumerable<AtualizarReceitaItemRequest> Componentes { get; set; } = [];
    
    public bool UsoContinuo { get; set; } = false;
    public TipoCalculoUsoContinuo? TipoUsoContinuo { get; set; }
    public decimal? QuantidadeReSell { get; set; }
    public PeriodosPosologia? PeriodicidadeReSell { get; set; }
}

public class AtualizarReceitaItemRequest
{
    public Guid ProdutoId { get; set; }
    public Guid? ProdutoSinonimoId { get; set; }
    public string DescricaoRotulo { get; set; }
    public decimal Quantidade { get; set; }
    public int UnidadeMedidaId { get; set; }
    public TipoComponente TipoQuantificacao { get; set; }
    public int Ordem { get; set; }
    public bool OcultaRotulo { get; set; }
}