using System.ComponentModel;
using Bootis.Producao.Domain.Enumerations;

namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;

public class ObterHistoricoTecnicoResponse
{
    public List<HistoricoTecnicoReceita> HistoricoTecnico { get; set; } = new();
}

public class HistoricoTecnicoReceita
{
    public Guid Id { get; set; }
    public DateTime DataHora { get; set; }
    public TipoRegistroTecnico TipoRegistro { get; set; }
    public string Descricao { get; set; } = string.Empty;
    public string[] ProdutosEnvolvidos { get; set; } = Array.Empty<string>();
    public string Usuario { get; set; } = string.Empty;

    public string TipoRegistroDescricao
    {
        get
        {
            var descriptionAttribute = typeof(TipoRegistroTecnico)
                .GetField(TipoRegistro.ToString())
                ?.GetCustomAttributes(typeof(DescriptionAttribute), false)
                as DescriptionAttribute[];

            return descriptionAttribute != null && descriptionAttribute.Length > 0
                ? descriptionAttribute[0].Description
                : TipoRegistro.ToString();
        }
    }
}
