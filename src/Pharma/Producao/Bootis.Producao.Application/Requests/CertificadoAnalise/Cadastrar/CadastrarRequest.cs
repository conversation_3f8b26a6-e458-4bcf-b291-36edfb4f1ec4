using Bootis.Producao.Domain.Dtos.CertificadoAnalise;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Producao.Application.Requests.CertificadoAnalise.Cadastrar;

public class CadastrarRequest : IRequest
{
    public Guid LoteId { get; set; }
    public decimal QuantidadeAmostragem { get; set; }
    public UnidadeMedidaAbreviacao UnidadeAmostragemId { get; set; }
    public IEnumerable<ControleQualidade> ControleQualidade { get; set; }
    public string InformacaoesComplementares { get; set; }
    public bool Aprovado { get; set; }
}

public class ControleQualidade : ICertificadoAnaliseEspecificacaoDto
{
    public Guid EnsaioId { get; set; }
    public string Especificacoes { get; set; }
    public string Resultado { get; set; }
}