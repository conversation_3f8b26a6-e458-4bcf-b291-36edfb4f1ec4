namespace Bootis.Producao.Application.Requests.CertificadoAnalise.Obter;

public class ObterResponse
{
    public int NumeroCertificado { get; set; }
    public bool Aprovado { get; set; }
    public DateTime DataEmissao { get; set; }
    public Guid ProdutoId { get; set; }
    public string ProdutoDescricao { get; set; }
    public Guid LoteId { get; set; }
    public string LoteNumero { get; set; }
    public Guid FornecedorId { get; set; }
    public string FornecedorNome { get; set; }
    public Guid PaisOrigemId { get; set; }
    public string PaisOrigemDescricao { get; set; }
    public decimal QuantidadeTotal { get; set; }
    public int UnidadeEstoqueId { get; set; }
    public string UnidadeMedidaAbreviacao { get; set; }
    public decimal PesoMolecular { get; set; }
    public string CasNumero { get; set; }
    public string DcbNumero { get; set; }
    public decimal QuantidadeAmostragem { get; set; }
    public int UnidadeAmostragemId { get; set; }
    public string UnidadeAmostragemAbreviacao { get; set; }
    public IEnumerable<ObterControleQualidadeResponse> ControleQualidade { get; set; }
    public string InformacoesComplementares { get; set; }
}

public class ObterControleQualidadeResponse
{
    public Guid EnsaioId { get; set; }
    public string EnsaioDescricao { get; set; }
    public string Especificacoes { get; set; }
    public string Resultado { get; set; }
}
