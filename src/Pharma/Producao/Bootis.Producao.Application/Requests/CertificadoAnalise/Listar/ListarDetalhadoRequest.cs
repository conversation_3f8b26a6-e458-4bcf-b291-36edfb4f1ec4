using Bootis.Shared.Common.Query;
using MediatR;

namespace Bootis.Producao.Application.Requests.CertificadoAnalise.Listar;

public class ListarDetalhadoRequest : Filter, IRequest<PaginatedResult<ListarDetalhadoResponse>>
{
    public List<Guid> ProdutoIds { get; set; }
    public List<Guid> LoteIds { get; set; }
    public bool? SomenteAprovados { get; set; }
    public DateTime? DataEmissaoDe { get; set; }
    public DateTime? DataEmissaoAte { get; set; }
}
