using Bootis.Producao.Application.Requests.ReceitaManipulada.Validar;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;

namespace Bootis.Producao.Application.Services.Interfaces;

public interface IValidarIncompatibilidadeAppService
{
    Task ValidarIncompatibilidadesBloqueioAsync(IEnumerable<Guid> produtoIds);
    Task ValidarIncompatibilidadesBloqueioAsync(IEnumerable<Guid> produtoIds, ReceitaManipulada receita);

    Task<IncompatibilidadeResponse> ObterCenariosIncompatibilidadeAsync(IEnumerable<Guid> produtoIds);
    Task<IncompatibilidadeResponse> ValidarERegistrarIncompatibilidadesAsync(ReceitaManipulada receita, IEnumerable<Guid> produtoIds, Guid usuarioId);
}