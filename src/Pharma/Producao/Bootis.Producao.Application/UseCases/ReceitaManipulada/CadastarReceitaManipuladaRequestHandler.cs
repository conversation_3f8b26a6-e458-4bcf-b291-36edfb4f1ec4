using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate;
using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Producao.Application.Extensions;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Cadastrar;
using Bootis.Producao.Application.Services.Interfaces;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Domain.Events;
using Bootis.Producao.Domain.Factories;
using Bootis.Shared.Application.Events;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class CadastrarReceitaManipuladaRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IClienteRepository clienteRepository,
    IFormaFarmaceuticaRepository formaFarmaceuticaRepository,
    IPrescritorRepository prescritorRepository,
    IProdutoRepository produtoRepository,
    IProdutoSinonimoRepository produtoSinonimoRepository,
    IReceitaManipuladaRepository receitaManipuladaRepository,
    IUsuarioRepository usuarioRepository,
    IReceitaUpsellAppService upsellService,
    IMediator mediator,
    ICalculoReceitaManipuladaFactory calculoReceitaFactory,
    IValidarIncompatibilidadeAppService validadorIncompatibilidade)
    : IRequestHandler<CadastrarReceitaManipuladaRequest,
        CadastrarReceitaManipuladaResponse>
{
    public async Task<CadastrarReceitaManipuladaResponse> Handle(CadastrarReceitaManipuladaRequest request,
        CancellationToken cancellationToken)
    {
        var formaFarmaceutica =
            await formaFarmaceuticaRepository.ObterFormaFarmaceuticaAsync(request.FormaFarmaceuticaId);
        var paciente = await clienteRepository.ObterClienteAsync(request.PacienteId);
        var usuario = await usuarioRepository.ObterUsuarioAsync(userContext.UserId);

        Prescritor prescritor = null;

        if (request.PrescritorId.HasValue)
            prescritor = await prescritorRepository.ObterPrescritorAsync(request.PrescritorId.Value);

        var (receita, resultado) =
            await CadastrarReceitaManipulada(request, paciente, prescritor, formaFarmaceutica);

        receita.GerarHistorico(usuario.Id, StatusReceita.Orcada);
        
        receitaManipuladaRepository.Add(receita);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        await mediator.Publish(
            new DomainEventNotification<ReceitaManipuladaCriadaComPedidoVendaEvent>(
                new ReceitaManipuladaCriadaComPedidoVendaEvent(
                    receita.Id,
                    request.PedidoVendaId
                )
            ),
            cancellationToken
        );

        var receitaDto = receita.MapearParaResultadoDetalhesDto(resultado);

        var upsell = await upsellService.GerarUpsellAsync(
            resultado,
            receita.QuantidadeReceita
        );

        return new CadastrarReceitaManipuladaResponse
        {
            Receita = receitaDto,
            Upsell = upsell
        };
    }

    private async Task<(Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada receita,
            CalculoReceitaManipulada resultado)>
        CadastrarReceitaManipulada(CadastrarReceitaManipuladaRequest request, Cliente paciente, Prescritor prescritor,
            FormaFarmaceutica formaFarmaceutica)
    {
        AjustarOrdemItens(request.Componentes);
        
        await ValidarIncompatibilidades(request.Componentes);

        var receitaManipulada = CriarReceitaManipulada(request, paciente, prescritor, formaFarmaceutica);

        foreach (var itemRequest in request.Componentes)
        {
            var produto = await produtoRepository.ObterProdutoAsync(itemRequest.ProdutoId);
            ProdutoSinonimo produtoSinonimo = null;
            var descricao = itemRequest.DescricaoRotulo?.Trim();

            if (itemRequest.ProdutoSinonimoId.HasValue)
            {
                await produtoSinonimoRepository.ValidarProdutoVinculadoAsync(
                    itemRequest.ProdutoId,
                    itemRequest.ProdutoSinonimoId.Value);

                produtoSinonimo = await produtoSinonimoRepository.ObterProdutoSinonimoAsync(
                    itemRequest.ProdutoSinonimoId.Value);

                if (string.IsNullOrWhiteSpace(descricao) && !string.IsNullOrWhiteSpace(produtoSinonimo?.Sinonimo))
                    descricao = produtoSinonimo.Sinonimo;
            }

            if (string.IsNullOrWhiteSpace(descricao))
                descricao = produto.Descricao;

            receitaManipulada.AdicionarItemReceita(
                produto,
                descricao,
                itemRequest.Quantidade,
                itemRequest.UnidadeMedidaId,
                itemRequest.TipoQuantificacao,
                itemRequest.Ordem,
                itemRequest.OcultaRotulo,
                produtoSinonimo,
                produtoSinonimo?.FatorEquivalencia,
                produtoSinonimo?.PercentualCorrecao
            );
        }
        
        var produtoIds = request.Componentes.Select(c => c.ProdutoId).Distinct().ToList();
        await validadorIncompatibilidade.ValidarIncompatibilidadesBloqueioAsync(produtoIds, receitaManipulada);
        
        await validadorIncompatibilidade.ValidarERegistrarIncompatibilidadesAsync(
            receitaManipulada,
            produtoIds,
            userContext.UserId);

        var resultado = calculoReceitaFactory.Criar(
            receitaManipulada.Itens,
            receitaManipulada.QuantidadeReceita,
            receitaManipulada.FormaFarmaceutica,
            receitaManipulada.Paciente,
            receitaManipulada.PrescritorId,
            receitaManipulada.TipoDescontoManual,
            receitaManipulada.DescontoManual,
            receitaManipulada.PercentualDescontoManual
        );

        await resultado.ExecutarAsync();

        receitaManipulada.AplicarResultadoCalculo(resultado);

        return (receitaManipulada, resultado);
    }

    private async Task ValidarIncompatibilidades(IEnumerable<CadastrarReceitaItemRequest> componentes)
    {
        var produtoIds = componentes.Select(c => c.ProdutoId).Distinct().ToList();
        await validadorIncompatibilidade.ValidarIncompatibilidadesBloqueioAsync(produtoIds);
    }

    private static void AjustarOrdemItens(IEnumerable<CadastrarReceitaItemRequest> itens)
    {
        var itensOrdenados = itens.OrderBy(i => i.Ordem).ToList();

        for (var i = 0; i < itensOrdenados.Count; i++) itensOrdenados[i].Ordem = i + 1;
    }

    private static Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada CriarReceitaManipulada(
        CadastrarReceitaManipuladaRequest request, Cliente paciente, Prescritor prescritor,
        FormaFarmaceutica formaFarmaceutica)
    {
        return new Domain.AggregatesModel.ReceitaManipuladaAggregate.ReceitaManipulada(paciente, prescritor,
            request.DataPrescricao, formaFarmaceutica, request.QuantidadeReceita, request.QuantidadeRepetir,
            request.QuantidadeDose);
    }
}