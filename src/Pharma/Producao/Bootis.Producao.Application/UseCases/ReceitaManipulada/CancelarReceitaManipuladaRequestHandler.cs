using Bootis.Producao.Application.Requests.ReceitaManipulada.Atualizar;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Domain.Events;
using Bootis.Producao.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class CancelarReceitaManipuladaRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IMediator mediator,
    IReceitaManipuladaRepository receitaManipuladaRepository,
    ILogger<CancelarReceitaManipuladaRequestHandler> logger)
    : IRequestHandler<CancelarReceitaManipuladaRequest>
{
    public async Task Handle(CancelarReceitaManipuladaRequest request, CancellationToken cancellationToken)
    {
        var receitaManipulada = await receitaManipuladaRepository.ObterReceitaManipuladaPorIdAsync(request.ReceitaManipuladaId)
            ?? throw new ValidationException(
                Localizer.Instance.GetMessage_ReceitaManipulada_GuidNaoEncontrado(request.ReceitaManipuladaId));
        
        if (receitaManipulada.Status is StatusReceita.Cancelada)
        {
            logger.LogInformation("Receita manipulada {ReceitaId} já está cancelada. Status atual: {Status}",
                request.ReceitaManipuladaId, receitaManipulada.Status);
            return;
        }
        
        if (string.IsNullOrWhiteSpace(request.MotivoCancelamento))
        {
            throw new ValidationException("O motivo do cancelamento é obrigatório.");
        }
        
        var statusAnterior = receitaManipulada.Status;
        
        receitaManipulada.CancelarReceita(userContext.UserId, request.MotivoCancelamento);

        receitaManipuladaRepository.Update(receitaManipulada);
        await unitOfWork.SaveChangesAsync(cancellationToken);
        
        await mediator.Publish(
            new Bootis.Shared.Application.Events.DomainEventNotification<ReceitaManipuladaStatusAlteradoEvent>(
                new ReceitaManipuladaStatusAlteradoEvent(
                    receitaManipulada.Id,
                    StatusReceita.Cancelada,
                    statusAnterior,
                    userContext.UserId)),
            cancellationToken);

        logger.LogInformation("Receita manipulada {ReceitaId} cancelada com sucesso. Motivo: {Motivo}. Usuário: {UserId}",
            request.ReceitaManipuladaId, request.MotivoCancelamento, userContext.UserId);
    }
}