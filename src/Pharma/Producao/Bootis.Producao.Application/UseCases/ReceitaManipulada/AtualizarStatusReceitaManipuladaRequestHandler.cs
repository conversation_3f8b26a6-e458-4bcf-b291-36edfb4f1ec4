using Bootis.Producao.Application.Requests.ReceitaManipulada.Atualizar;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Domain.Events;
using Bootis.Producao.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class AtualizarStatusReceitaManipuladaRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IMediator mediator,
    IReceitaManipuladaRepository receitaManipuladaRepository,
    ILogger<AtualizarStatusReceitaManipuladaRequestHandler> logger)
    : IRequestHandler<AtualizarStatusReceitaManipuladaRequest>
{
    public async Task Handle(AtualizarStatusReceitaManipuladaRequest request, CancellationToken cancellationToken)
    {
        var receitaManipulada = await receitaManipuladaRepository.ObterReceitaManipuladaPorIdAsync(request.ReceitaManipuladaId)
            ?? throw new ValidationException(
                Localizer.Instance.GetMessage_ReceitaManipulada_GuidNaoEncontrado(request.ReceitaManipuladaId));
        
        if (request.Status is StatusReceita.Cancelada)
        {
            logger.LogWarning("Tentativa de cancelar receita manipulada {ReceitaId} através do endpoint de atualização de status. Use o endpoint de cancelamento.",
                request.ReceitaManipuladaId);

            throw new ValidationException("Para cancelar uma receita, utilize o endpoint específico de cancelamento que requer um motivo.");
        }
        
        if (receitaManipulada.Status == request.Status)
        {
            logger.LogInformation("Receita manipulada {ReceitaId} já está no status {Status}. Nenhuma alteração necessária.",
                request.ReceitaManipuladaId, request.Status);
            return;
        }
        
        var statusAnterior = receitaManipulada.Status;

        receitaManipulada.AtualizarStatus(request.Status, userContext.UserId);

        receitaManipuladaRepository.Update(receitaManipulada);
        await unitOfWork.SaveChangesAsync(cancellationToken);
        
        if (request.Status == StatusReceita.Rejeitada)
        {
            await mediator.Publish(
                new Bootis.Shared.Application.Events.DomainEventNotification<ReceitaManipuladaStatusAlteradoEvent>(
                    new ReceitaManipuladaStatusAlteradoEvent(
                        receitaManipulada.Id,
                        StatusReceita.Rejeitada,
                        statusAnterior,
                        userContext.UserId)),
                cancellationToken);
        }

        logger.LogInformation("Status da receita manipulada {ReceitaId} atualizado com sucesso de {StatusAnterior} para {NovoStatus}. Usuário: {UserId}",
            request.ReceitaManipuladaId, receitaManipulada.Status, request.Status, userContext.UserId);
    }
}