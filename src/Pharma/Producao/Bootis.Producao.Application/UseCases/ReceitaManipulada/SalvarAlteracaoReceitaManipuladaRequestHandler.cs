using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Producao.Application.Extensions;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Atualizar;
using Bootis.Producao.Domain.AggregatesModel.PosologiaAggregate;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Domain.Factories;
using Bootis.Producao.Domain.Services.ReceitaManipulada;
using Bootis.Producao.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Common.ValueObjects.Responses;
using MediatR;
using ValidationException = System.ComponentModel.DataAnnotations.ValidationException;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class ConfirmarAlteracaoReceitaManipuladaHandler(
    IUnitOfWork unitOfWork,
    IReceitaManipuladaRepository repository,
    IReceitaManipuladaDomainService receitaService,
    IClienteRepository clienteRepository,
    IPrescritorRepository prescritorRepository,
    IPosologiaRepository posologiaRepository,
    IProdutoSinonimoRepository produtoSinonimoRepository,
    IProdutoRepository produtoRepository,
    ICalculoReceitaManipuladaFactory calculoReceitaFactory)
    : IRequestHandler<SalvarReceitaManipuladaRequest, CadastrarGlobalResponse>
{
    public async Task<CadastrarGlobalResponse> Handle(SalvarReceitaManipuladaRequest request,
        CancellationToken cancellationToken)
    {
        var receita = await repository.ObterReceitaManipuladaPorIdAsync(request.Id)
                      ?? throw new ValidationException(
                          Localizer.Instance.GetMessage_ReceitaManipulada_GuidNaoEncontrado(request.Id));

        var (capsulaAtual, embalagemAtual) = ObterCapsulaEmbalagemAtuais(receita.RastreioCalculos, receita.Itens);

        await repository.RemoverRastreiosPorReceitaId(receita.Id);
        await repository.RemoverCalculosPorReceitaId(receita.Id);
        await repository.RemoverBaseCalculoPorReceitaId(receita.Id);
        await repository.RemoverValoresPorReceitaId(receita.Id);

        var paciente = await clienteRepository.ObterPorIdAsync(request.PacienteId);
        var prescritor = request.PrescritorId.HasValue
            ? await prescritorRepository.ObterPorIdAsync(request.PrescritorId.Value)
            : null;
        var posologia = request.PosologiaId.HasValue
            ? await posologiaRepository.ObterPorIdAsync(request.PosologiaId.Value)
            : null;

        if (posologia != null && posologia.FormaFarmaceuticaId != receita.FormaFarmaceuticaId)
            throw new ValidationException("A posologia selecionada não pertence à mesma forma farmacêutica da receita."); //TODO: add no i18n

        receita.AlterarPaciente(paciente);
        receita.AlterarPrescritor(prescritor);
        receita.AlterarPosologia(posologia?.Id);
        receita.Posologia = posologia;
        receita.AlterarQuantidadeReceita(request.QuantidadeReceita);
        receita.AlterarQuantidadeRepetir(request.QuantidadeRepetir);
        receita.AlterarQuantidadeDose(request.QuantidadeDose);
        receita.AlterarObservacao(request.Observacao);

        var periodicidade = posologia?.Periodo ?? request.PeriodicidadeReSell;
        var dose = posologia?.QuantidadeDosePorPeriodo ?? request.QuantidadeDose;

        receita.AplicarUsoContinuo(
            request.UsoContinuo,
            request.TipoUsoContinuo,
            request.QuantidadeReSell,
            periodicidade,
            dose
        );

        receita.LimparItens();

        foreach (var itemRequest in request.Componentes)
        {
            ProdutoSinonimo produtoSinonimo = null;
            var descricao = itemRequest.DescricaoRotulo;

            var produto = await produtoRepository.ObterProdutoAsync(itemRequest.ProdutoId);
            if (string.IsNullOrWhiteSpace(descricao))
                descricao = produto.Descricao;

            if (itemRequest.ProdutoSinonimoId.HasValue)
            {
                await produtoSinonimoRepository.ValidarProdutoVinculadoAsync(
                    itemRequest.ProdutoId,
                    itemRequest.ProdutoSinonimoId.Value
                );

                produtoSinonimo =
                    await produtoSinonimoRepository.ObterProdutoSinonimoAsync(itemRequest.ProdutoSinonimoId.Value);
                descricao ??= produtoSinonimo.DescricaoRotulo ?? produto.Descricao;
            }

            receita.AdicionarItemReceita(
                produto,
                descricao,
                itemRequest.Quantidade,
                itemRequest.UnidadeMedidaId,
                itemRequest.TipoQuantificacao,
                itemRequest.Ordem,
                itemRequest.OcultaRotulo,
                produtoSinonimo,
                produtoSinonimo?.FatorEquivalencia,
                produtoSinonimo?.PercentualCorrecao
            );
        }

        if (request.TipoDesconto.HasValue)
            receita.DefinirDescontoManual(
                request.Desconto ?? 0,
                request.PercentualDesconto ?? 0,
                request.TipoDesconto.Value
            );

        if (request.ProdutoCapsulaId.HasValue)
        {
            var capsulaNova = await repository.ObterProdutoPorIdAsync(request.ProdutoCapsulaId.Value);
            if (capsulaNova.Id != capsulaAtual?.Id)
                await receita.AlterarCapsulaAsync(capsulaNova.Id, receitaService);
        }

        if (request.ProdutoEmbalagemId.HasValue)
        {
            var embalagemProduto = await repository.ObterProdutoPorIdAsync(request.ProdutoEmbalagemId.Value);
            if (embalagemProduto.Id != embalagemAtual?.Id)
            {
                var embalagem = await repository.ObterProdutoEmbalagemPorIdAsync(embalagemProduto.Id);
                await receita.AlterarEmbalagemAsync(embalagem);
            }
        }

        var resultado = calculoReceitaFactory.Criar(
            receita.Itens,
            receita.QuantidadeReceita,
            receita.FormaFarmaceutica,
            receita.Paciente,
            receita.PrescritorId,
            receita.TipoDescontoManual,
            receita.DescontoManual,
            receita.PercentualDescontoManual
        );

        await resultado.ExecutarAsync();
        
        receita.CalcularEAplicarTerminoUsoContinuo();

        receita.AplicarResultadoCalculo(resultado);

        await unitOfWork.SaveChangesAsync(cancellationToken);

        return new CadastrarGlobalResponse { Id = receita.Id };
    }

    private static (Produto capsula, Produto embalagem) ObterCapsulaEmbalagemAtuais(
        IEnumerable<ReceitaManipuladaRastreioCalculo> rastreios,
        IEnumerable<ReceitaManipuladaItem> itens)
    {
        var capsula = rastreios
                          .FirstOrDefault(r =>
                              r.Produto.ClasseProdutoId is TipoClasseProdutoAbreviacao.TipoCapsula &&
                              r.TipoOrigem is TipoOrigem.TipoCapsula or TipoOrigem.Original)
                          ?.Produto
                      ?? itens
                          .FirstOrDefault(i => i.Produto.ClasseProdutoId is TipoClasseProdutoAbreviacao.TipoCapsula)
                          ?.Produto;

        var embalagem = rastreios
                            .FirstOrDefault(r =>
                                r.Produto.ClasseProdutoId is TipoClasseProdutoAbreviacao.Embalagem &&
                                r.TipoOrigem is TipoOrigem.Embalagem or TipoOrigem.Original)
                            ?.Produto
                        ?? itens
                            .FirstOrDefault(i => i.Produto.ClasseProdutoId is TipoClasseProdutoAbreviacao.Embalagem)
                            ?.Produto;

        return (capsula, embalagem);
    }
}