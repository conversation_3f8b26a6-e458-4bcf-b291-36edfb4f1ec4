using Microsoft.Extensions.Localization;

namespace Bootis.Producao.Resources;

public static class TranslateExtensions
{
    public static string GetMessage_ProdutoTipoCapsula_GuidNaoEncontrado(this IStringLocalizer localizer, Guid guid)
    {
        return string.Format(localizer["ProdutoTipoCapsula_GuidNaoEncontrado"].Value, guid);
    }

    public static string GetMessage_ProdutoMateriaPrimaCapsulaPronta_GuidNaoEncontrado(this IStringLocalizer localizer,
        Guid id)
    {
        return string.Format(localizer["ProdutoMateriaPrimaCapsulaPronta_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_ProdutoCapsulaPronta_GuidNaoEncontrado(this IStringLocalizer localizer, Guid guid)
    {
        return string.Format(localizer["ProdutoCapsulaPronta_GuidNaoEncontrado"].Value, guid);
    }

    public static string GetMessage_FormaFarmaceutica_IdNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["FormaFarmaceutica_IdNaoEncontrado"].Value, id);
    }

    public static string GetMessage_Posologia_DescricaoExistente(this IStringLocalizer localizer, string descricao)
    {
        return string.Format(localizer["Posologia_DescricaoExistente"].Value, descricao);
    }

    public static string GetMessage_Posologia_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Posologia_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_FormulaPadraoItem_GuidNaoEncontrado(this IStringLocalizer localizer,
        Guid id)
    {
        return string.Format(localizer["FormulaPadraoItem_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_FormulaPadraoItem_QuantidadeInvalida(this IStringLocalizer localizer)
    {
        return string.Format(localizer["FormulaPadraoItem_GuidNaoEncontrado"].Value);
    }

    public static string GetMessage_LocalEstoque_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["LocalEstoque_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_FormulaPadrao_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["FormulaPadrao_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_FormulaPadrao_ProdutoExistente(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["FormulaPadrao_ProdutoExistente"].Value, id);
    }

    public static string GetMessage_FormaFarmaceutica_GuidEstaAtiva(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["FormaFarmaceutica_GuidEstaAtiva"].Value, id);
    }

    public static string GetMessage_Capsula_NaoDisponivel(this IStringLocalizer localizer)
    {
        return string.Format(localizer["Capsula_NaoDisponivel"].Value);
    }

    public static string GetMessage_EmbalagemAssociacao_GuidNaoEncontrado(this IStringLocalizer localizer, Guid guid)
    {
        return string.Format(localizer["EmbalagemAssociacao_GuidNaoEncontrado"].Value, guid);
    }

    public static string GetMessage_EmbalagemCapsulaAssociacao_GuidNaoEncontrado(this IStringLocalizer localizer,
        Guid guid)
    {
        return string.Format(localizer["EmbalagemCapsulaAssociacao_GuidNaoEncontrado"].Value, guid);
    }

    public static string GetMessage_Embalagem_NaoDisponivel(this IStringLocalizer localizer)
    {
        return localizer["Embalagem_NaoDisponivel"].Value;
    }

    public static string GetMessage_ReceitaManipuladaItem_DataValidadeNaoEncontrada(this IStringLocalizer localizer,
        string descricaoProduto)
    {
        return string.Format(localizer["ReceitaManipuladaItem_DataValidadeNaoEncontrada"].Value, descricaoProduto);
    }

    public static string GetMessage_ReceitaManipuladaItem_OrdemInvalida(this IStringLocalizer localizer, int ordem)
    {
        return string.Format(localizer["ReceitaManipuladaItem_OrdemInvalida"].Value, ordem);
    }

    public static string GetMessage_ReceitaManipulada_GuidNaoEncontrado(this IStringLocalizer localizer, Guid guid)
    {
        return string.Format(localizer["ReceitaManipulada_GuidNaoEncontrado"].Value, guid);
    }

    public static string GetMessage_ReceitaManipuladaItem_IdNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["ReceitaManipuladaItem_IdNaoEncontrado"].Value, id);
    }

    public static string GetMessage_ReceitaManipuladaItem_FatoresNaoEncontrados(this IStringLocalizer localizer,
        Guid id)
    {
        return string.Format(localizer["ReceitaManipuladaItem_FatoresNaoEncontrados"].Value, id);
    }

    public static string GetMessage_ReceitaManipulada_MateriaPrimaNaoEncontrada(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ReceitaManipulada_MateriaPrimaNaoEncontrada"].Value);
    }

    public static string GetMessage_ReceitaManipulada_UnidadeMedidaQspInvalida(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ReceitaManipulada_UnidadeMedidaQspInvalida"].Value);
    }

    public static string GetMessage_ReceitaManipulada_QuantidadeReceitaInvalida(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ReceitaManipulada_QuantidadeReceitaInvalida"].Value);
    }

    public static string GetMessage_ReceitaManipulada_DosagemTipoCalculoInvalido(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ReceitaManipulada_DosagemTipoCalculoInvalido"].Value);
    }

    public static string GetMessage_ReceitaManipulada_ClasseBiofarmaceuticaInvalida(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ReceitaManipulada_ClasseBiofarmaceuticaInvalida"].Value);
    }

    public static string GetMessage_ReceitaManipulada_ExcipienteNaoEncontrado(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ReceitaManipulada_ExcipienteNaoEncontrado"].Value);
    }

    public static string GetMessage_ReceitaManipuladaItem_FormaFarmaceuticaInvalida(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ReceitaManipuladaItem_FormaFarmaceuticaInvalida"].Value);
    }
    
    public static string GetMessage_ReceitaManipulada_StatusInvalido(this IStringLocalizer localizer, string status)
    {
        return string.Format(localizer["ReceitaManipulada_StatusInvalido"].Value);
    }

    public static string GetMessage_CertificadoAnalise_IdNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["CertificadoAnalise_IdNaoEncontrado"].Value, id);
    }
}