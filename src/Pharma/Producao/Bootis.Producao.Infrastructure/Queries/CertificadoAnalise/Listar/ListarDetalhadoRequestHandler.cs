
using Bootis.Producao.Application.Requests.CertificadoAnalise.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;
using System.Data;

namespace Bootis.Producao.Infrastructure.Queries.CertificadoAnalise.Listar;

public class ListarDetalhadoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoRequest, PaginatedResult<ListarDetalhadoResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoResponse>> Handle(ListarDetalhadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT ca.id AS certificado_analise_id,
                                  ca.sequencia_group_tenant AS numero_certificado,
                                  prod.id AS produto_id,
                                  prod.descricao AS produto_descricao,
                                  ca.lote_id,
                                  l.numero AS lote_numero,
                                  COALESCE(SUM(se.saldo), 0) AS quantidade_total,
                                  prod.unidade_estoque_id,
                                  un.abreviacao AS unidade_estoque_abreviacao,
                                  f.id AS fornecedor_id,
                                  f.nome AS fornecedor_nome,
                                  ca.data_emissao,
                                  ca.aprovado
                             FROM certificados_analise ca
                                  LEFT JOIN lotes l ON
                                            l.id = ca.lote_id
                                  LEFT JOIN saldos_estoque se ON
                                            se.lote_id = l.id
                                  LEFT JOIN produtos prod ON
                                            prod.id = l.produto_id
                                  LEFT JOIN fornecedores f ON
                                            f.id = l.fornecedor_id
                                  LEFT JOIN unidades_medida un ON
                                            un.id = prod.unidade_estoque_id
                            WHERE ca.group_tenant_id = @groupTenantId 
                                  !@SEARCH_CONDITION@!
                         GROUP BY ca.id,
                                  ca.sequencia_group_tenant,
                                  prod.id,
                                  prod.descricao,
                                  ca.lote_id,
                                  l.numero,
                                  prod.unidade_estoque_id,
                                  un.abreviacao,
                                  f.id,
                                  f.nome,
                                  ca.data_emissao,
                                  ca.aprovado;
                         """;

        var conditionalProduto = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "prod.id = ANY ( @ProdutoIds )",
            Predicate = filter => filter.ProdutoIds != null
        };

        var conditionalLote = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "l.id = ANY ( @LoteIds )",
            Predicate = filter => filter.LoteIds != null
        };

        var conditionalAprovado = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "ca.aprovado = @SomenteAprovados",
            Predicate = filter => filter.SomenteAprovados != null
        };

        var conditionalPeriodoEmissao = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "ca.data_emissao BETWEEN @DataEmissaoDe AND @DataEmissaoAte",
            Predicate = filter => filter.DataEmissaoDe.HasValue && filter.DataEmissaoAte.HasValue
        };

        var searchProduto = new StringSearchField
        {
            Field = "prod.descricao",
            CompareType = StringCompareType.Contains
        };

        var searchNumeroLote = new StringSearchField
        {
            Field = "l.numero",
            CompareType = StringCompareType.Contains
        };

        var searchFornecedor = new StringSearchField
        {
            Field = "f.nome",
            CompareType = StringCompareType.Contains
        };

        var searchDataEmissao = new DateSearchField
        {
            Field = "ca.data_emissao",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        return PaginatedQueryBuilder<ListarDetalhadoRequest, ListarDetalhadoResponse>
            .Create(connection, sql, request, userContext)
            .AddFilter(conditionalProduto)
            .AddFilter(conditionalLote)
            .AddFilter(conditionalAprovado)
            .AddFilter(conditionalPeriodoEmissao)
            .AddSearchField(searchProduto)
            .AddSearchField(searchNumeroLote)
            .AddSearchField(searchFornecedor)
            .AddSearchField(searchDataEmissao)
            .ExecuteAsync();
    }
}
