using Bootis.Producao.Application.Requests.CertificadoAnalise.Obter;
using Bootis.Producao.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;
using System.Data;

namespace Bootis.Producao.Infrastructure.Queries.CertificadoAnalise.Obter;

public class ObterRequestHandler(IUserContext userContext, IDbConnection connection) : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT ca.sequencia_group_tenant AS numero_certificado,
                                  ca.data_emissao,
                                  p.id AS produto_id,
                                  p.descricao AS produto_descricao,
                                  ca.lote_id,
                                  l.numero AS lote_numero,
                                  f.id AS fornecedor_id,
                                  f.nome AS fornecedor_nome,
                                  pa.id AS pais_origem_id,
                                  pa.descricao AS pais_origem_descricao,
                                  COALESCE(SUM(se.saldo), 0) AS quantidade_total,
                                  p.unidade_estoque_id,
                                  un1.abreviacao AS unidade_medida_abreviacao,
                                  mp.peso_molecular_sal AS peso_molecular,
                                  cas.numero_cas AS cas_numero,
                                  dcb.numero_dcb AS dcb_numero,
                                  ca.quantidade_amostragem,
                                  ca.unidade_amostragem_id,
                                  un2.abreviacao AS unidade_amostragem_abreviacao,
                                  ca.informacoes_complementares,
                                  ca.aprovado
                             FROM certificados_analise ca
                                  LEFT JOIN lotes l ON
                                            l.id = ca.lote_id
                                  LEFT JOIN saldos_estoque se on 
                                  		  se.lote_id = l.id
                                  LEFT JOIN produtos p ON
                                            p.id = l.produto_id
                                  LEFT JOIN fornecedores f ON
                                            f.id = l.fornecedor_id
                                  LEFT JOIN lotes_informacao_tecnica lit on
                                  		  lit.lote_id = l.id
                                  LEFT JOIN paises pa ON
                                            pa.id = lit.pais_origem_id
                                  LEFT JOIN produtos_materia_prima mp ON
                                            mp.produto_id = p.id
                                  LEFT JOIN cas cas ON
                                            cas.id = mp.cas_id
                                  LEFT JOIN dcbs dcb ON
                                            dcb.id = mp.dcb_id
                                  LEFT JOIN unidades_medida un1 ON
                                            un1.id = p.unidade_estoque_id
                                  LEFT JOIN unidades_medida un2 ON
                                            un2.id = ca.unidade_amostragem_id
                            WHERE ca.id = @id
                              AND ca.group_tenant_id = @groupTenantId
                           GROUP BY ca.sequencia_group_tenant,
                                    ca.data_emissao,
                                    p.id,
                                    p.descricao,
                                    ca.lote_id,
                                    l.numero,
                                    f.id,
                                    f.nome,
                                    pa.id,
                                    pa.descricao,
                                    p.unidade_estoque_id,
                                    un1.abreviacao,
                                    mp.peso_molecular_sal,
                                    mp.cas_id,
                                    cas.numero_cas,
                                    mp.dcb_id,
                                    dcb.numero_dcb,
                                    ca.quantidade_amostragem,
                                    ca.unidade_amostragem_id,
                                    un2.abreviacao,
                                    ca.informacoes_complementares,
                                    ca.aprovado;

                           SELECT cae.certificado_analise_id,
                                  cae.ensaio_controle_qualidade_id AS ensaio_id,
                                  ecq.descricao AS ensaio_descricao,
                                  cae.especificacoes,
                                  cae.resultado
                             FROM certificados_analise_especificacao cae
                                  LEFT JOIN ensaios_controle_qualidade ecq ON
                                            ecq.id = cae.ensaio_controle_qualidade_id
                            WHERE cae.certificado_analise_id = @id;
                           """;

        await using var query = await connection.QueryMultipleAsync(sql, new { id = request.Id, groupTenantId = userContext.GroupTenantId });

        var response = await query.ReadFirstOrDefaultAsync<ObterResponse>();

        if (response == null)
        {
            throw new DomainException(
                Localizer.Instance.GetMessage_CertificadoAnalise_IdNaoEncontrado(request.Id));
        }

        response.ControleQualidade = await query.ReadAsync<ObterControleQualidadeResponse>();

        return response;
    }
}
