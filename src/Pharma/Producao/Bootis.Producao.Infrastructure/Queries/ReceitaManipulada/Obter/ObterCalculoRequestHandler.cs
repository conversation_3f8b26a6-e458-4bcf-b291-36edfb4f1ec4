using System.Data;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Producao.Infrastructure.Queries.ReceitaManipulada.Obter;

public class ObterCalculoRequestHandler(
    IDbConnection connection)
    : IRequestHandler<ObterCalculoRequest, ObterCalculoResponse>
{
    public async Task<ObterCalculoResponse> Handle(ObterCalculoRequest request, CancellationToken cancellationToken)
    {
        const string sql = """

                               SELECT rm.id,
                                      ff.descricao AS descricao_forma_farmaceutica, 
                                      rm.quantidade_receita, 
                                      rm.quantidade_dose,
                                      ff.apresentacao AS apresentacao_forma_farmaceutica,
                                      rm.quantidade_repetir,
                                      rbc.volume_total AS volume_calculado, 
                                      1 AS quantidade_embalagens,
                                      rm.previsao_entrega
                                 FROM receitas_manipuladas rm
                                      LEFT JOIN formas_farmaceutica ff ON ff.id = rm.forma_farmaceutica_id
                                      LEFT JOIN receitas_manipuladas_base_calculos rbc 
                                        ON rbc.receita_manipulada_id = rm.id
                                WHERE rm.id = @Id;

                                SELECT rr.id AS componente_id,
                                   CASE 
                                     WHEN rr.tipo_origem = 1 THEN rr_origem.produto_id 
                                     WHEN rr.tipo_origem = 3 THEN rr_origem.produto_id 
                                     ELSE NULL 
                                   END AS produto_origem_id,
                                   CASE WHEN rr.tipo_origem = 1 THEN p_assoc.id ELSE p.id END AS componente_produto_id,
                                   CASE 
                                     WHEN rr.receita_manipulada_item_id IS NOT NULL 
                                          AND rmis.id IS NOT NULL 
                                          AND ps.sinonimo IS NOT NULL 
                                          AND ps.sinonimo <> p.descricao
                                     THEN ps.sinonimo
                                     ELSE NULL
                                   END AS descricao_produto_sinonimo,
                                   CASE 
                                     WHEN ri.descricao_produto IS NOT NULL 
                                          AND ri.descricao_produto <> p.descricao
                                          AND (ps.sinonimo IS NULL OR ri.descricao_produto <> ps.sinonimo)
                                     THEN ri.descricao_produto
                                     ELSE NULL
                                   END AS descricao_produto_editada,
                                   p.descricao AS descricao_produto_original,
                                   mp.is_excipiente,
                                   mp.is_qsp,
                                   rr.quantidade AS quantidade_prescrita,
                                   rr.unidade_medida_id AS unidade_medida_prescrita_id,
                                   um.abreviacao AS unidade_medida_prescrita_abreviacao,
                                   rc.quantidade_calculada,
                                   rc.unidade_medida_calculada_id,
                                   um_calc.abreviacao AS unidade_medida_calculada_abreviacao,
                                   rr.quantidade_volume,
                                   rr.unidade_medida_volume_id,
                                   rc.fator_total AS fator,
                                   CASE WHEN rr.tipo_origem = 1 THEN p_assoc.valor_custo ELSE p.valor_custo END AS preco_custo,
                                   CASE WHEN rr.tipo_origem = 1 THEN p_assoc.margem_lucro ELSE p.margem_lucro END AS percentual_lucro,
                                   CASE WHEN rr.tipo_origem = 1 THEN p_assoc.valor_venda ELSE p.valor_venda END AS preco_venda,
                                   CASE WHEN rr.tipo_origem = 1 THEN p_assoc.classe_produto_id ELSE p.classe_produto_id END AS classe_produto,
                                   CASE WHEN rr.tipo_origem = 1 THEN fp_assoc.id ELSE fp.id END AS formula_padrao_id,
                                   rr.formula_desmembra AS desmembra_formula,
                                   CASE 
                                     WHEN rr.receita_manipulada_rastreio_calculo_id IS NOT NULL 
                                          AND fp.id IS NOT NULL 
                                          AND fp.formula_padrao_desmembramento IN (1, 2) THEN 3
                                     ELSE rr.tipo_origem
                                   END AS tipo_origem,
                                   CASE 
                                       WHEN rr.tipo_origem = 1 AND fp_assoc.id IS NOT NULL THEN 0
                                       WHEN rr.tipo_origem <> 1 AND fp.id IS NOT NULL THEN 0
                                       ELSE CASE 
                                                WHEN rr.tipo_origem = 1 THEN p_assoc.classe_produto_id
                                                ELSE p.classe_produto_id
                                            END
                                   END AS tipo_componente,
                                   CASE 
                                     WHEN rr.receita_manipulada_item_id IS NOT NULL AND rmis.id IS NOT NULL THEN true
                                     ELSE false
                                   END AS eh_sinonimo,
                                       p.id AS produto_original_id,
                                   CASE
                                     WHEN ri.descricao_produto IS NOT NULL 
                                          AND ri.descricao_produto <> p.descricao
                                          AND (ps.sinonimo IS NULL OR ri.descricao_produto <> ps.sinonimo)
                                     THEN true
                                     ELSE false
                                   END AS eh_editado_no_rotulo,
                                   CASE 
                                       WHEN rr.tipo_origem = 1 THEN fp_assoc.formula_padrao_desmembramento
                                       ELSE fp.formula_padrao_desmembramento
                                   END AS tipo_desmembramento
                                   FROM receita_manipulada_rastreio_calculos rr
                                       LEFT JOIN receitas_manipuladas rm ON rm.id = rr.receita_manipulada_id
                                       LEFT JOIN produtos p ON p.id = rr.produto_id
                                       LEFT JOIN formulas_padrao fp ON fp.produto_id = p.id
                                       LEFT JOIN receita_manipulada_rastreio_calculos rr_origem ON rr_origem.id = rr.receita_manipulada_rastreio_calculo_id
                                       LEFT JOIN produtos_associado pa ON pa.produto_id = rr_origem.produto_id AND pa.produto_associado_id = rr.produto_id
                                       LEFT JOIN produtos p_assoc ON p_assoc.id = pa.produto_associado_id
                                       LEFT JOIN receitas_manipuladas_calculos rc ON rc.receita_manipulada_rastreio_calculo_id = rr.id
                                       LEFT JOIN unidades_medida um ON um.id = rr.unidade_medida_id
                                       LEFT JOIN unidades_medida um_calc ON um_calc.id = rc.unidade_medida_calculada_id
                                       LEFT JOIN formulas_padrao fp_assoc ON fp_assoc.produto_id = p_assoc.id
                                       LEFT JOIN produtos_materia_prima mp ON mp.produto_id = CASE WHEN rr.tipo_origem = 1 THEN p_assoc.id ELSE p.id END
                                       LEFT JOIN receitas_manipuladas_item ri ON ri.id = rr.receita_manipulada_item_id
                                       LEFT JOIN receitas_manipuladas_item_sinonimo rmis ON rmis.id = rr.receita_manipulada_item_id
                                       LEFT JOIN produtos_sinonimo ps ON ps.id = rmis.produto_sinonimo_id
                                       LEFT JOIN produtos p_original ON p_original.id = ps.produto_id
                                     WHERE rm.id = @Id;

                               SELECT rmv.valor_bruto, rmv.valor_desconto, rmv.valor_total 
                                 FROM receitas_manipuladas_valores rmv
                                      LEFT JOIN receitas_manipuladas rm ON rm.id = rmv.receita_manipulada_id
                                WHERE rm.id = @Id;
                                   
                           """;

        await using var multi = await connection.QueryMultipleAsync(sql, new { request.Id });

        var response = await multi.ReadSingleOrDefaultAsync<ObterCalculoResponse>();

        if (response is null)
            throw new DomainException(Localizer.Instance
                .GetMessage_ReceitaManipulada_GuidNaoEncontrado(request.Id));

        var componentesRaw = await multi.ReadAsync<ObterComponentesCalculados>();

        response.ValoresReceita = await multi.ReadFirstOrDefaultAsync<ObterValoresCalculados>();

        AjustaDesmembramento(componentesRaw);

        await AjustaCapsulaEAssociadoAsync(componentesRaw);

        response.ComponentesCalculados = FiltraComponentes(componentesRaw);
        return response;
    }

    private static void AjustaDesmembramento(IEnumerable<ObterComponentesCalculados> comps)
    {
        var formulas = comps
            .Where(c => c.TipoComponente == TipoComponenteReceita.Formula &&
                        c.DesmembraFormula &&
                        c.TipoDesmembramento is FormulaPadraoDesmembramento.DesmembraMesmaFicha
                            or FormulaPadraoDesmembramento.DesmembraSeparaFicha)
            .ToList();

        foreach (var f in formulas)
        {
            var filhos = comps
                .Where(x =>
                    x.ProdutoOrigemId == f.ComponenteProdutoId &&
                    x.TipoOrigem == TipoOrigem.Desmembrado
                )
                .ToList();

            filhos.ForEach(fh =>
            {
                fh.EhSinonimo = false;
                fh.EhEditadoNoRotulo = false;
                fh.DescricaoProdutoEditada = null;
                fh.DescricaoProdutoSinonimo = null;
            });

            f.ComponentesFormula = filhos;
        }
    }

    private async Task AjustaCapsulaEAssociadoAsync(IEnumerable<ObterComponentesCalculados> comps)
    {
        const string sqlCapsulas = """
                                       SELECT ct.numero_capsula,
                                              ct.volume_ml
                                         FROM produtos_tipo_capsula ptc
                                              JOIN capsulas_tamanho ct ON ct.id = ptc.numero_capsula_id
                                        WHERE ptc.produto_id = @ProdutoId;
                                   """;

        const string sqlAssociado = """
                                        SELECT quantidade_associada,
                                               acumula,
                                               dosagem_minima, 
                                               dosagem_maxima,
                                               unidade_medida_dosagem
                                          FROM produtos_associado
                                         WHERE produto_id = @ProdutoOrigemId
                                           AND produto_associado_id = @ProdutoId;
                                    """;

        foreach (var c in comps)
        {
            if (c.TipoComponente == TipoComponenteReceita.TipoCapsula)
            {
                var cap = await connection.QueryFirstOrDefaultAsync<CapsulaInfo>(
                    sqlCapsulas, new { ProdutoId = c.ComponenteProdutoId });

                if (cap is not null)
                {
                    c.TamanhoCapsula = cap.Numero;
                    c.VolumeMaximoCapsula = cap.Volume;
                }
            }

            if (c.TipoOrigem == TipoOrigem.Associado && c.ProdutoOrigemId.HasValue)
            {
                var assoc = await connection.QueryFirstOrDefaultAsync<DadosAssociado>(
                    sqlAssociado,
                    new
                    {
                        ProdutoId = c.ComponenteProdutoId,
                        ProdutoOrigemId = c.ProdutoOrigemId.Value
                    });

                if (assoc is not null)
                {
                    c.QuantidadeAssociada = assoc.QuantidadeAssociada;
                    c.Acumula = assoc.Acumula;
                    c.DosagemMinima = assoc.DosagemMinima;
                    c.DosagemMaxima = assoc.DosagemMaxima;
                    c.UnidadeMedidaDosagemId = assoc.UnidadeMedidaDosagem;
                }
            }
        }
    }

    private static List<ObterComponentesCalculados> FiltraComponentes(IEnumerable<ObterComponentesCalculados> comps)
    {
        var idsFilhosDeFormulaDesmembrada = comps
            .Where(c => c.ComponentesFormula?.Any() ?? false)
            .SelectMany(f => f.ComponentesFormula)
            .Select(f => f.ComponenteId)
            .ToHashSet();

        return comps
            .Where(c =>
                !idsFilhosDeFormulaDesmembrada.Contains(c.ComponenteId) &&
                (c.TipoOrigem != TipoOrigem.Desmembrado || c.ProdutoOrigemId == null))
            .ToList();
    }

    private record CapsulaInfo(int Numero, decimal Volume);

    private class DadosAssociado
    {
        public decimal QuantidadeAssociada { get; set; }
        public bool Acumula { get; set; }
        public decimal DosagemMinima { get; set; }
        public decimal DosagemMaxima { get; set; }
        public int UnidadeMedidaDosagem { get; set; }
    }
}