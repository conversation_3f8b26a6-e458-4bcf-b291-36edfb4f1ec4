using Asp.Versioning;
using Bootis.Producao.Application.Requests.CertificadoAnalise.Cadastrar;
using Bootis.Producao.Application.Requests.CertificadoAnalise.Obter;
using Bootis.Producao.Application.Requests.CertificadoAnalise.Listar;
using Bootis.Shared.Common.Query;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Producao.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Producao")]
[Route("producao/v{version:apiVersion}/[controller]")]
public class CertificadoAnaliseController(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Cadastrar([FromBody] CadastrarRequest request)
    {
        await mediator.Send(request);

        return Ok();
    }

    [HttpGet]
    [Route("{id:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Obter(Guid id)
    {
        var request = new ObterRequest{ Id = id } ;

        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarDetalhado")]
    [ProducesResponseType(typeof(PaginatedResult<ListarDetalhadoResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Obter([FromQuery] ListarDetalhadoRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarLotes")]
    [ProducesResponseType(typeof(PaginatedResult<ListarLoteResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ListarLotes([FromQuery] ListarLoteRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }
}
