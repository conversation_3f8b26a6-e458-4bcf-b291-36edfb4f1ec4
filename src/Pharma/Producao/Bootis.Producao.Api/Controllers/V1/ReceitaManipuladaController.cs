using Asp.Versioning;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Atualizar;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Cadastrar;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Listar;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;
using Bootis.Producao.Application.Requests.ReceitaManipulada.Validar;
using Bootis.Producao.Domain.Dtos.ReceitaManipulada;
using Bootis.Shared.Api.Attributes;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Common.ValueObjects.Responses;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Producao.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Producao")]
[Route("producao/v{version:apiVersion}/[controller]")]
public class ReceitaManipuladaController(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [Route("CadastrarReceitaManipulada")]
    [ProducesResponseType(typeof(CadastrarReceitaManipuladaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CadastrarItemReceitaManipulada(CadastrarReceitaManipuladaRequest command)
    {
        var result = await mediator.Send(command);

        return Ok(result);
    }
    
    [HttpPost]
    [Route("ValidarIncompatibilidade")]
    [ProducesResponseType(typeof(IncompatibilidadeResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ValidarIncompatibilidade(ValidarIncompatibilidadesRequest command)
    {
        var result = await mediator.Send(command);
        
        return Ok(result);
    }

    [HttpPut]
    [Route("AtualizarReceitaManipulada")]
    [ProducesResponseType(typeof(AtualizarReceitaManipuladaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AtualizarReceitaManipulada([FromBody] AtualizarReceitaManipuladaRequest command)
    {
        var result = await mediator.Send(command);
        return Ok(result);
    }

    [HttpPut]
    [Route("AtualizarReceitaManipulada/Detalhes")]
    [ProducesResponseType(typeof(ReceitaManipuladaResultadoDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AtualizarDetalhesReceitaManipulada(
        [FromBody] AtualizarDetalhesReceitaManipuladaPutRequest command)
    {
        var result = await mediator.Send(command);
        return Ok(result);
    }

    [HttpPut]
    [Route("AtualizarReceitaManipulada/Recalcular")]
    [ProducesResponseType(typeof(RecalcularReceitaManipuladaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RecalcularReceitaManipulada(
        [FromBody] RecalcularReceitaManipuladaRequest command)
    {
        var result = await mediator.Send(command);
        return Ok(result);
    }

    [HttpPut]
    [Route("AtualizarReceitaManipulada/SalvarAlteracao")]
    [ProducesResponseType(typeof(CadastrarGlobalResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> SalvarAlteracaoReceitaManipulada(
        [FromBody] SalvarReceitaManipuladaRequest command)
    {
        var result = await mediator.Send(command);
        return Ok(result);
    }

    [HttpPatch]
    [Route("{id:Guid}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AtualizarDetalhes([FromRoute] Guid id,
        [FromBody] JsonPatchDocument<AtualizarDetalhesReceitaManipuladaRequest> patchDocument)
    {
        var request = new PatchReceitaManipuladaRequest
        {
            Id = id,
            PatchDocument = patchDocument
        };

        await mediator.Send(request);

        return Ok();
    }

    [HttpPut]
    [Route("Rotulo/Atualizar")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> AtualizarRotuloReceitaManipulada(
        [FromBody] AtualizarReceitaManipuladaRotuloRequest command)
    {
        await mediator.Send(command);

        return Ok();
    }

    [HttpPut("AtualizarStatus")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Producao_ReceitaManipulada_AlterarStatus)]
    public async Task<IActionResult> AtualizarStatusReceita(
        [FromBody] AtualizarStatusReceitaManipuladaRequest command)
    {
        await mediator.Send(command);
        return Ok();
    }

    [HttpPut("Cancelar")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Producao_ReceitaManipulada_Cancelar)]
    public async Task<IActionResult> CancelarReceita(
        [FromBody] CancelarReceitaManipuladaRequest command)
    {
        await mediator.Send(command);
        return Ok();
    }

    [HttpGet]
    [Route("{id:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Obter(Guid id)
    {
        var result = await mediator.Send(new ObterRequest { Id = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterHistorico/{id:Guid}")]
    [ProducesResponseType(typeof(ObterHistoricoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterHistorico(Guid id)
    {
        var request = new ObterHistoricoRequest { ReceitaManipuladaId = id };

        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterHistoricoTecnico/{id:Guid}")]
    [ProducesResponseType(typeof(ObterHistoricoTecnicoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterHistoricoTecnico(Guid id)
    {
        var request = new ObterHistoricoTecnicoRequest { ReceitaManipuladaId = id };

        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterConferencia/{id:Guid}")]
    [ProducesResponseType(typeof(ObterConferenciaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Producao_ReceitaManipulada_VerDetalhes)]
    public async Task<IActionResult> ObterConferencia(Guid id)
    {
        var result = await mediator.Send(new ObterConferenciaRequest { Id = id });
        return Ok(result);
    }

    [HttpGet]
    [Route("ObterReceitaComponentes/{id:Guid}")]
    [ProducesResponseType(typeof(ObterReceitaComponentesResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterReceitaComponentes(Guid id)
    {
        var result = await mediator.Send(new ObterReceitaComponentesRequest { Id = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("ListarDetalhado")]
    [ProducesResponseType(typeof(PaginatedResult<ListarDetalhadoResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Producao_ReceitaManipulada_VerLista)]
    public async Task<IActionResult> ListarDetalhado([FromQuery] ListarDetalhadoRequest command)
    {
        var result = await mediator.Send(command);
        return Ok(result);
    }

    [HttpGet]
    [Route("ListarPosologiaReceita")]
    [ProducesResponseType(typeof(PaginatedResult<ListarPosologiaReceitaResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Producao_Posologia_VerLista)]
    public async Task<IActionResult> ListarPosologiaReceita([FromQuery] ListarPosologiaReceitaRequest command)
    {
        var result = await mediator.Send(command);
        return Ok(result);
    }

    [HttpGet]
    [Route("ListarCapsulasReceita")]
    [ProducesResponseType(typeof(PaginatedResult<ListarCapsulaReceitaResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Producao_ReceitaManipulada_VerLista)]
    [AuthorizeRoles(Permissoes.Estoque_Produtos_VerLista)]
    public async Task<IActionResult> ListarCapsulasReceita([FromQuery] ListarCapsulaReceitaRequest command)
    {
        var result = await mediator.Send(command);
        return Ok(result);
    }

    [HttpGet]
    [Route("ListarEmbalagensReceita")]
    [ProducesResponseType(typeof(PaginatedResult<ListarEmbalagemReceitaResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Producao_ReceitaManipulada_VerLista)]
    [AuthorizeRoles(Permissoes.Estoque_Produtos_VerLista)]
    public async Task<IActionResult> ListarEmbalagensReceita([FromQuery] ListarEmbalagemReceitaRequest command)
    {
        var result = await mediator.Send(command);
        return Ok(result);
    }
    
    [HttpGet]
    [Route("ListarPorCliente")]
    [ProducesResponseType(typeof(PaginatedResult<ListarPorClienteResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    [AuthorizeRoles(Permissoes.Producao_ReceitaManipulada_VerLista)]
    public async Task<IActionResult> ListarPorCliente([FromQuery] ListarPorClienteRequest command)
    {
        var result = await mediator.Send(command);
        return Ok(result);
    }
    
    [HttpGet]
    [Route("ListarResell")]
    [ProducesResponseType(typeof(IEnumerable<ListarResellResponse>), StatusCodes.Status200OK)]
    [AuthorizeRoles(Permissoes.Producao_ReceitaManipulada_VerLista)]
    public async Task<IActionResult> ListarResell([FromQuery] ListarResellRequest request)
    {
        var result = await mediator.Send(request);
        return Ok(result);
    }
    
    [HttpGet]
    [Route("ListarConferencia")]
    [ProducesResponseType(typeof(IEnumerable<ListarConferenciaResponse>), StatusCodes.Status200OK)]
    [AuthorizeRoles(Permissoes.Producao_ReceitaManipulada_VerLista)]
    public async Task<IActionResult> ListarConferencia([FromQuery] ListarConferenciaRequest request)
    {
        var result = await mediator.Send(request);
        return Ok(result);
    }

    [HttpGet]
    [Route("ObterCalculoReceita/{id:Guid}")]
    [ProducesResponseType(typeof(ObterCalculoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterCalculoReceita(Guid id)
    {
        var result = await mediator.Send(new ObterCalculoRequest { Id = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterDetalhesValoresReceita/{id:Guid}")]
    [ProducesResponseType(typeof(ObterDetalhesValoresResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterDetalhesValoresReceita(Guid id)
    {
        var result = await mediator.Send(new ObterDetalhesValoresRequest { ReceitaId = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterDetalhesVolumesReceita/{id:Guid}")]
    [ProducesResponseType(typeof(ObterDetalhesVolumesResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterDetalhesVolumesReceita(Guid id)
    {
        var result = await mediator.Send(new ObterDetalhesVolumesRequest { ReceitaId = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterDetalhesDescontosReceita/{id:Guid}")]
    [ProducesResponseType(typeof(ObterDetalhesDescontosResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterDetalhesDescontosReceita(Guid id)
    {
        var result = await mediator.Send(new ObterDetalhesDescontosRequest { ReceitaId = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterDetalhesItemReceita/{id:Guid}")]
    [ProducesResponseType(typeof(ObterDetalhesItemReceitaResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterDetalhesItemReceita(Guid id)
    {
        var result = await mediator.Send(new ObterDetalhesItemReceitaRequest { ReceitaId = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterFatoresReceitaItem/{id:Guid}")]
    [ProducesResponseType(typeof(ObterFatoresResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterFatoresReceitaItem(Guid id)
    {
        var result = await mediator.Send(new ObterFatoresRequest { ComponenteId = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("Rotulo/ListarDetalhado")]
    [ProducesResponseType(typeof(PaginatedResult<ListarDetalhadoRotuloResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ListarDetalhadoRotuloReceita([FromQuery] ListarDetalhadoRotuloRequest command)
    {
        var result = await mediator.Send(command);

        return Ok(result);
    }

    [HttpGet]
    [Route("{id:Guid}/Rotulo")]
    [ProducesResponseType(typeof(ObterRotuloResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterRotuloReceita(Guid id)
    {
        var result = await mediator.Send(new ObterRotuloRequest { Id = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("OrdemManipulacao/ListarDetalhado")]
    [ProducesResponseType(typeof(PaginatedResult<ListarDetalhadoOrdemManipulacaoResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ListarDetalhadoOrdemManipulacaoReceita(
        [FromQuery] ListarDetalhadoOrdemManipulacaoRequest command)
    {
        var result = await mediator.Send(command);

        return Ok(result);
    }
}