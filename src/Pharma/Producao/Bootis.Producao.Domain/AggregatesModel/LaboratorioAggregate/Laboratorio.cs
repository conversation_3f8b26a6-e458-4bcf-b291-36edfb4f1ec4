using Bootis.Catalogo.Domain.AggregatesModel.ModeloOrdemManipulacaoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.LaboratorioAggregate;

public class Laboratorio() : Entity, IAggregateRoot, ITenant
{
    public Laboratorio(string nomeLaboratorio,
        Guid empresaId,
        Guid localEstoqueId,
        Guid modeloOrdemManipulacaoId) : this()
    {
        NomeLaboratorio = nomeLaboratorio;
        EmpresaId = empresaId;
        LocalEstoqueId = localEstoqueId;
        ModeloOrdemManipulacaoId = modeloOrdemManipulacaoId;
        Ativo = true;
    }

    public string NomeLaboratorio { get; private set; }
    public bool Ativo { get; private set; }
    public Guid EmpresaId { get; private set; }
    public Guid LocalEstoqueId { get; private set; }
    public Guid ModeloOrdemManipulacaoId { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void AtualizarLaboratorio(string nome, Empresa empresa, Guid localEstoqueId, Guid modeloOrdemManipulacaoId)
    {
        NomeLaboratorio = nome;
        Empresa = empresa;
        LocalEstoqueId = localEstoqueId;
        ModeloOrdemManipulacaoId = modeloOrdemManipulacaoId;
    }

    public void AtualizarStatus(bool ativo)
    {
        Ativo = ativo;
    }

    #region Navigation properties

    public virtual Empresa Empresa { get; private set; }
    public virtual LocalEstoque LocalEstoque { get; set; }
    public virtual ModeloOrdemManipulacao ModeloOrdemManipulacao { get; set; }

    #endregion
}