using Bootis.Shared.Common.Events;

namespace Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate.Events;

public class IncompatibilidadeLiberacaoSolicitadaEvent : IDomainEvent
{
    public Guid ReceitaManipuladaId { get; init; }
    public IEnumerable<Guid> ProdutoIds { get; init; } = new List<Guid>();
    public string TokenUsoUnico { get; init; } = string.Empty;
    public string MotivoLiberacao { get; init; } = string.Empty;
    public Guid UsuarioId { get; init; }
    public DateTime DataHoraSolicitacao { get; init; } = DateTime.UtcNow;
}