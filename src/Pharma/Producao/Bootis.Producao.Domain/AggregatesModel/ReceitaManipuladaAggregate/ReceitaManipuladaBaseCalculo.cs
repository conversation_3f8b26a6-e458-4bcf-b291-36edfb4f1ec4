using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;

public class ReceitaManipuladaBaseCalculo : Entity
{
    protected ReceitaManipuladaBaseCalculo()
    {
    }

    public ReceitaManipuladaBaseCalculo(Guid receitaManipuladaId, decimal volumeMateriaPrima,
        decimal volumeExcipienteMinimo, decimal volumeTotal, RegraEscolhaExcipiente regraEscolhaExcipiente,
        DateOnly dataValidade, RegraDataValidade regraDataValidade, decimal quantidadeDivisaoDose)
    {
        ReceitaManipuladaId = receitaManipuladaId;
        VolumeMateriaPrima = volumeMateriaPrima;
        VolumeExcipienteMinimo = volumeExcipienteMinimo;
        VolumeTotal = volumeTotal;
        RegraEscolhaExcipiente = regraEscolhaExcipiente;
        DataValidade = dataValidade;
        RegraDataValidade = regraDataValidade;
        QuantidadeDivisaoDose = quantidadeDivisaoDose;
    }

    public virtual ReceitaManipulada ReceitaManipulada { get; set; }
    public Guid ReceitaManipuladaId { get; set; }
    public decimal VolumeMateriaPrima { get; set; }
    public decimal VolumeExcipienteMinimo { get; set; }
    public decimal VolumeTotal { get; set; }
    public RegraEscolhaExcipiente RegraEscolhaExcipiente { get; set; }
    public DateOnly DataValidade { get; set; }
    public RegraDataValidade RegraDataValidade { get; set; }
    public decimal QuantidadeDivisaoDose { get; set; }

    public void Atualizar(decimal volumeMateriaPrima, decimal volumeExcipienteMinimo, decimal volumeTotal,
        RegraEscolhaExcipiente regraEscolhaExcipiente, DateOnly dataValidade,
        RegraDataValidade regraDataValidade, decimal divisaoDose)
    {
        VolumeMateriaPrima = volumeMateriaPrima;
        VolumeExcipienteMinimo = volumeExcipienteMinimo;
        VolumeTotal = volumeTotal;
        RegraEscolhaExcipiente = regraEscolhaExcipiente;
        DataValidade = dataValidade;
        RegraDataValidade = regraDataValidade;
        QuantidadeDivisaoDose = divisaoDose;
    }
}