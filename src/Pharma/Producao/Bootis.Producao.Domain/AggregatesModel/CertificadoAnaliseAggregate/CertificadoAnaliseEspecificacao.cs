using Bootis.Catalogo.Domain.AggregatesModel.BibliografiaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.EnsaioControleQualidadeAggregate;

namespace Bootis.Producao.Domain.AggregatesModel.CertificadoAnaliseAggregate;

public class CertificadoAnaliseEspecificacao()
{
    public CertificadoAnaliseEspecificacao(CertificadoAnalise certificadoAnalise,
                                           Guid ensaioControleQualidadeId,
                                           string especificacoes,
                                           string resultado) : this()
    {
        CertificadoAnalise = certificadoAnalise;
        EnsaioControleQualidadeId = ensaioControleQualidadeId;
        Especificacoes = especificacoes;
        Resultado = resultado;
    }

    public Guid CertificadoAnaliseId { get; set; }
    public Guid EnsaioControleQualidadeId { get; set; }
    public string Especificacoes { get; set; }
    public string Resultado { get; set; }

    #region Navigation Properties

    public virtual CertificadoAnalise CertificadoAnalise { get; set; }
    public virtual EnsaioControleQualidade EnsaioControleQualidade { get; set; }

    #endregion
}
