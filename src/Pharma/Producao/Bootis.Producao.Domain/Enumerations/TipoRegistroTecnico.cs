using System.ComponentModel;

namespace Bootis.Producao.Domain.Enumerations;

public enum TipoRegistroTecnico
{
    [Description("Incompatibilidade Liberada com Senha")]
    IncompatibilidadeLiberadaComSenha = 1,

    [Description("Aviso de Incompatibilidade")]
    AvisoIncompatibilidade = 2,

    [Description("Validação Farmacotécnica")]
    ValidacaoFarmacotecnica = 3,

    [Description("Alteração de Fórmula")]
    AlteracaoFormula = 4,

    [Description("Exceção Aprovada")]
    ExcecaoAprovada = 5,

    [Description("Observação Técnica")]
    ObservacaoTecnica = 6
}