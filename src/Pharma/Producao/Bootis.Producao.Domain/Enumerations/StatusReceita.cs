using System.ComponentModel;

namespace Bootis.Producao.Domain.Enumerations;

public enum StatusReceita
{
    [Description("Orçada")]
    Orcada = 1,
    
    [Description("Aguardando Conferência")]
    AguardandoConferencia = 2,
    
    [Description("Conferência Finalizada")]
    ConferenciaFinalizada = 3,
    
    [Description("Pesagem Finalizada")]
    PesagemFinalizada = 4,
    
    [Description("Peso Médio Finalizado")]
    PesoMedioFinalizado = 5,
    
    [Description("Pronta para Entrega")]
    ProntaParaEntrega = 6,
    
    Entregue = 7,
    
    Cancelada = 8,
    
    Rejeitada = 9
}