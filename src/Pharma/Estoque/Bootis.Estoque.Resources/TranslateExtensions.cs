using Microsoft.Extensions.Localization;

namespace Bootis.Estoque.Resources;

public static class TranslateExtensions
{
    public static string GetMessage_Produto_GuidJaExisteNoPedidoDeCompra(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Produto_GuidJaExisteNoPedidoDeCompra"].Value, id);
    }

    public static string GetMessage_MotivoPerda_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["MotivoPerda_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_LancamentoPerda_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["LancamentoPerda_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_OperacaoEstoque_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["OperacaoEstoque_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_OperacaoEstoque_IdNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["OperacaoEstoque_IdNaoEncontrado"].Value, id);
    }

    public static string GetMessage_SaldoInformado_Invalido(this IStringLocalizer localizer)
    {
        return string.Format(localizer["SaldoInformado_Invalido"].Value);
    }

    public static string GetMessage_SaldoEstoque_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["SaldoEstoque_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_SaldoEstoqueLocal_GuidNaoEncontrado(this IStringLocalizer localizer, Guid loteId,
        Guid localEstoqueId)
    {
        return string.Format(localizer["SaldoEstoqueLocal_GuidNaoEncontrado"].Value, loteId, localEstoqueId);
    }

    public static string GetMessage_LocalEstoque_DevePertencerAMesmaEmpresa(this IStringLocalizer localizer,
        Guid id)
    {
        return string.Format(localizer["LocalEstoque_DevePertencerAMesmaEmpresa"].Value, id);
    }

    public static string GetMessage_LocalEstoque_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["LocalEstoque_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_LocalEstoque_IdNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["LocalEstoque_IdNaoEncontrado"].Value, id);
    }

    public static string GetMessage_LocalEstoque_DescricaoExistente(this IStringLocalizer localizer, string descricao,
        Guid empresaId)
    {
        return string.Format(localizer["LocalEstoque_DescricaoExistente"].Value, descricao, empresaId);
    }

    public static string GetMessage_LocalEstoque_EmUso(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["LocalEstoque_EmUso"].Value, id);
    }

    public static string GetMessage_AjusteSaldoEstoque_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["AjusteSaldoEstoque_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_UnidadeMedida_Invalida(this IStringLocalizer localizer, int id)
    {
        return string.Format(localizer["UnidadeMedida_Invalida"].Value, id);
    }

    public static string GetMessage_UnidadeMedida_GuidNaoEncontrado(this IStringLocalizer localizer, int id)
    {
        return string.Format(localizer["UnidadeMedida_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_UnidadeMedida_AbreviacaoExistente(this IStringLocalizer localizer, string campo)
    {
        return string.Format(localizer["UnidadeMedida_AbreviacaoExistente"].Value, campo, campo);
    }

    public static string GetMessage_ConversaUnidadeMedida_UnidadesJaCadastradas(this IStringLocalizer localizer,
        int unidadeOrigem, int unidadeConversao)
    {
        return string.Format(localizer["ConversaUnidadeMedida_UnidadesJaCadastradas"].Value, unidadeOrigem,
            unidadeConversao);
    }

    public static string GetMessage_ConversaoUnidadeMedida_IdNaoEncontrado(this IStringLocalizer localizer,
        int idOrigem, int idConversao)
    {
        return string.Format(localizer["ConversaoUnidadeMedida_IdNaoEncontrado"].Value, idOrigem, idConversao);
    }

    public static string GetMessage_Lote_BloqueadoOuInativo(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Lote_BloqueadoOuInativo"].Value, id);
    }

    public static string GetMessage_Lote_IdNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Lote_IdNaoEncontrado"].Value, id);
    }

    public static string GetMessage_Lote_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Lote_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_Lote_NumeroJaExistente(this IStringLocalizer localizer, string numero)
    {
        return string.Format(localizer["Lote_NumeroJaExistente"].Value, numero);
    }

    public static string GetMessage_Lote_DataValidadeVencida(this IStringLocalizer localizer)
    {
        return localizer["Lote_DataValidadeVencida"].Value;
    }

    public static string GetMessage_Lote_EmUso(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Lote_EmUso"].Value, id);
    }

    public static string GetMessage_Lote_QuantidadeInvalida(this IStringLocalizer localizer)
    {
        return string.Format(localizer["Lote_QuantidadeInvalida"].Value);
    }

    public static string GetMessage_Lote_DensidadeInvalida(this IStringLocalizer localizer, decimal densidade, string numeroLote)
    {
        return string.Format(localizer["Lote_DensidadeInvalida"].Value, densidade, numeroLote);
    }

    public static string GetMessage_Lote_NaoPossuiInformacaoTecnica(this IStringLocalizer localizer, Guid guid)
    {
        return string.Format(localizer["Lote_NaoPossuiInformacaoTecnica"].Value, guid);
    }

    public static string GetMessage_Lote_SituacaoDivergenteControleQualidade(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Lote_SituacaoDivergenteControleQualidade"].Value, id);
    }

    public static string GetMessage_LancamentoPerda_SaldoEstoqueInsuficiente(this IStringLocalizer localizer,
        decimal campo)
    {
        return string.Format(localizer["LancamentoPerda_SaldoEstoqueInsuficiente"].Value, campo, campo);
    }

    public static string GetMessage_MovimentoEstoque_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["MovimentoEstoque_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_SaldoEstoque_IdNaoEncontrado(this IStringLocalizer localizer, Guid loteId,
        Guid localEstoqueId)
    {
        return string.Format(localizer["SaldoEstoque_IdNaoEncontrado"].Value, loteId, localEstoqueId);
    }

    public static string GetMessage_TransferenciaLote_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["TransferenciaLote_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_TransferenciaLoteItem_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["TransferenciaLoteItem_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_LocalEstoque_NaoPodeSerIgual(this IStringLocalizer localizer)
    {
        return string.Format(localizer["LocalEstoque_NaoPodeSerIgual"].Value);
    }

    public static string GetMessage_LocalEstoque_NaoPossuiOProduto(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["LocalEstoque_NaoPossuiOProduto"].Value, id);
    }

    public static string GetMessage_Lote_NaoPossuiQuantidadeDeProduto(this IStringLocalizer localizer, Guid id,
        decimal quantidade)
    {
        return string.Format(localizer["Lote_NaoPossuiQuantidadeDeProduto"].Value, id, quantidade);
    }


    public static string GetMessage_Produto_GuidNaoPodeSerExcluido(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Produto_GuidNaoPodeSerExcluido"].Value, id);
    }

    public static string GetMessage_Produto_ComMovimentacao(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Produto_ComMovimentacao"].Value, id);
    }


    public static string GetMessage_ProdutoLoteEmUso_JaAssociado(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ProdutoLoteEmUso_JaAssociado"].Value);
    }

    public static string GetMessage_ProdutoLoteEmUso_NaoExiste(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ProdutoLoteEmUso_NaoExiste"].Value);
    }

    public static string GetMessage_ProdutoMensagem_IdNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["ProdutoMensagem_IdNaoEncontrado"].Value, id);
    }

    public static string GetMessage_ProdutoAssociado_RecursaoDetectada(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ProdutoAssociado_RecursaoDetectada"].Value);
    }

    public static string GetMessage_ProdutoTipoCapsula_Invalido(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["ProdutoTipoCapulsa_Invalido"].Value, id);
    }

    public static string GetMessage_ProdutoEmbalagem_Invalido(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["ProdutoEmbalagem_Invalido"].Value, id);
    }


    public static string GetMessage_ProdutoDiluido_DiluicaoUnica(this IStringLocalizer localizer,
        Guid produtoId)
    {
        return string.Format(localizer["ProdutoDiluido_DiluicaoUnica"].Value, produtoId);
    }

    public static string GetMessage_ProdutoDiluido_FormaFarmaceuticaDiluicaoExistente(this IStringLocalizer localizer,
        Guid produtoDiluidoId, Guid formaFarmaceuticaId)
    {
        return string.Format(localizer["ProdutoDiluido_FormaFarmaceuticaDiluicaoExistente"].Value,
            produtoDiluidoId, formaFarmaceuticaId);
    }

    public static string GetMessage_ProdutoDiluido_FormaFarmaceuticaDiluicaoQualquerDosagem(
        this IStringLocalizer localizer, Guid produtoDiluidoId, Guid formaFarmaceuticaId)
    {
        return string.Format(localizer["ProdutoDiluido_FormaFarmaceuticaDiluicaoQualquerDosagem"].Value,
            produtoDiluidoId, formaFarmaceuticaId);
    }

    public static string GetMessage_ProdutoDiluido_CadastroDiluicaoUnicaInvalido(this IStringLocalizer localizer,
        Guid produtoId)
    {
        return string.Format(localizer["ProdutoDiluido_CadastroDiluicaoUnicaInvalido"].Value, produtoId);
    }

    public static string GetMessage_ProdutoSinonimo_SinonimoIgualAoProduto(this IStringLocalizer localizer,
        string descricao)
    {
        return string.Format(localizer["ProdutoSinonimo_SinonimoIgualAoProduto"].Value, descricao);
    }


    public static string GetMessage_SubGrupo_NaoPertenceAoGrupoPrincipal(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["SubGrupo_NaoPertenceAoGrupoPrincipal"].Value, id);
    }

    public static string GetMessage_SubGrupo_NaoPodeSerCadastradoComoGrupoPai(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["SubGrupo_NaoPodeSerCadastradoComoGrupoPai"].Value, id);
    }


    public static string GetMessage_Grupo_IdGrupoPaiNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Grupo_IdGrupoPaiNaoEncontrado"].Value, id);
    }

    public static string GetMessage_Grupo_GuidGrupoPaiNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Grupo_GuidGrupoPaiNaoEncontrado"].Value, id);
    }

    public static string GetMessage_Grupo_ExclusaoProibida(this IStringLocalizer localizer, float produtos,
        float subgrupos)
    {
        return string.Format(localizer["Grupo_ExclusaoProibida"].Value, produtos, subgrupos);
    }

    public static string GetMessage_Grupo_ExclusaoProibidaSubGrupo(this IStringLocalizer localizer, float subgrupos)
    {
        return string.Format(localizer["Grupo_ExclusaoProibidaSubGrupo"].Value, subgrupos);
    }

    public static string GetMessage_SubGrupo_ExclusaoProibida(this IStringLocalizer localizer, float produtos)
    {
        return string.Format(localizer["SubGrupo_ExclusaoProibida"].Value, produtos);
    }

    public static string GetMessage_ClassificacaoProduto_GuidNaoEncontrado(this IStringLocalizer localizer,
        Guid id)
    {
        return string.Format(localizer["ClassificacaoProduto_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_Inventario_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Inventario_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_Inventario_NaoPodeSerAtualizado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Inventario_NaoPodeSerAtualizado"].Value, id);
    }

    public static string GetMessage_Inventario_NaoPodeSerCancelado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Inventario_NaoPodeSerCancelado"].Value, id);
    }

    public static string GetSituacaoLote(this IStringLocalizer localizer, string situacao)
    {
        return localizer[$"SituacaoLote_{situacao}"].Value;
    }

    public static string GetMessage_Laboratorio_GuidNaoEncontrado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["Laboratorio_GuidNaoEncontrado"].Value, id);
    }

    public static string GetMessage_Laboratorio_DescricaoExistente(this IStringLocalizer localizer, string descricao)
    {
        return string.Format(localizer["Laboratorio_DescricaoExistente"].Value, descricao);
    }

    public static string GetMessage_SaldoEstoque_NaoEncontradoLoteId(this IStringLocalizer localizer, Guid loteId)
    {
        return string.Format(localizer["SaldoEstoque_NaoEncontradoLoteId"].Value, loteId);
    }

    public static string GetMessage_Inventario_NaoPodeAdicionarProduto(this IStringLocalizer localizer,
        Guid inventarioId)
    {
        return string.Format(localizer["Inventario_NaoPodeAdicionarProduto"].Value, inventarioId);
    }

    public static string GetMessage_Inventario_NaoPodeSerExcluido(this IStringLocalizer localizer,
        Guid inventarioId)
    {
        return string.Format(localizer["Inventario_NaoPodeSerExcluido"].Value, inventarioId);
    }

    public static string GetMessage_Inventario_NaoPodeIniciarLancamento(this IStringLocalizer localizer,
        Guid id)
    {
        return string.Format(localizer["Inventario_NaoPodeIniciarLancamento"].Value, id);
    }

    public static string GetMessage_Inventario_NaoPodeFinalizarLancamento(this IStringLocalizer localizer,
        Guid id)
    {
        return string.Format(localizer["Inventario_NaoPodeFinalizarLancamento"].Value, id);
    }

    public static string GetMessage_Inventario_NaoPodeSerRelancado(this IStringLocalizer localizer,
        Guid inventarioId)
    {
        return string.Format(localizer["Inventario_NaoPodeSerRelancado"].Value, inventarioId);
    }

    public static string GetMessage_Inventario_NaoPodeFinalizarConferencia(this IStringLocalizer localizer,
        Guid id)
    {
        return string.Format(localizer["Inventario_NaoPodeFinalizarConferencia"].Value, id);
    }

    public static string GetMessage_Inventario_LancamentoNaoPoderSerCancelado(this IStringLocalizer localizer,
        Guid id)
    {
        return string.Format(localizer["Inventario_LancamentoNaoPoderSerCancelado"].Value, id);
    }

    public static string GetMessage_Inventario_LocalEstoqueNaoVinculado(this IStringLocalizer localizer,
        Guid inventarioId, Guid localEstoqueId)
    {
        return string.Format(localizer["Inventario_LocalEstoqueNaoVinculado"].Value, localEstoqueId,
            inventarioId);
    }

    public static string GetMessage_Inventario_InventarioItensIdsDuplicados(this IStringLocalizer localizer)
    {
        return string.Format(localizer["Inventario_InventarioItensIdsDuplicados"].Value);
    }

    public static string GetMessage_Inventario_LancamentoComItensFaltando(this IStringLocalizer localizer)
    {
        return string.Format(localizer["Inventario_LancamentoComItensFaltando"].Value);
    }

    public static string GetMessage_Inventario_ProdutoExistenteNoLancamento(this IStringLocalizer localizer,
        Guid inventarioId, Guid loteId)
    {
        return string.Format(localizer["Inventario_ProdutoExistenteNoLancamento"].Value, loteId,
            inventarioId);
    }

    public static string GetMessage_SaldoEstoque_SaldoBloqueado(this IStringLocalizer localizer, Guid id)
    {
        return string.Format(localizer["SaldoEstoque_SaldoBloqueado"].Value, id);
    }

    public static string GetMessage_ProjecaoEstoque_IntervaloInvalido(this IStringLocalizer localizer)
    {
        return string.Format(localizer["ProjecaoEstoque_IntervaloInvalido"].Value);
    }

    public static string GetMessage_ProjecaoEstoque_DadosIncompletos(this IStringLocalizer localizer)
    {
        return localizer["ProjecaoEstoque_DadosIncompletos"];
    }

    public static string GetMessage_NecessidadeCompra_FiltroIncorreto(this IStringLocalizer localizer)
    {
        return string.Format(localizer["NecessidadeCompra_FiltroIncorreto"].Value);
    }
}