using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.ProdutoLoteEmUsoAggregate;

public class ProdutoLoteEmUso : Entity, IAggregateRoot
{
    public ProdutoLoteEmUso()
    {
    }

    public ProdutoLoteEmUso(Guid produtoId,
        Lote lote,
        Guid localEstoqueId) : this()
    {
        ProdutoId = produtoId;
        Lote = lote;
        LocalEstoqueId = localEstoqueId;
    }

    public ProdutoLoteEmUso(Guid produtoId,
        Guid loteId,
        Guid localEstoqueId) : this()
    {
        ProdutoId = produtoId;
        LoteId = loteId;
        LocalEstoqueId = localEstoqueId;
    }

    public Guid ProdutoId { get; private set; }
    public Guid LoteId { get; private set; }
    public Guid LocalEstoqueId { get; private set; }
    public virtual Produto Produto { get; set; }
    public virtual Lote Lote { get; private set; }
    public virtual LocalEstoque LocalEstoque { get; set; }

    public void AtualizarLote(Guid novoLoteId)
    {
        LoteId = novoLoteId;
    }
}