using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate.Events;
using Bootis.Estoque.Domain.Dtos.Lote;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Estoque.Resources;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Events;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;

public class Lote : Entity, IAggregateRoot, ITenant
{
    public Lote()
    {
    }

    public Lote(string numero,
        Produto produto,
        Fornecedor fornecedor,
        DateOnly dataFabricacao,
        DateOnly dataValidade,
        int numeroNf,
        int serieNf,
        Guid usuarioId,
        SituacaoLote situacao) : this()
    {
        Numero = numero;
        Produto = produto;
        Fornecedor = fornecedor;
        DataFabricacao = dataFabricacao;
        DataValidade = dataValidade;
        NumeroNf = numeroNf;
        SerieNf = serieNf;
        UsuarioId = usuarioId;
        Situacao = situacao;

        DataLancamento = DateTime.UtcNow;
    }

    public string Numero { get; private set; }
    public Guid ProdutoId { get; set; }
    public virtual Produto Produto { get; private set; }
    public Guid FornecedorId { get; set; }
    public virtual Fornecedor Fornecedor { get; private set; }
    public Guid UsuarioId { get; private set; }
    public virtual Usuario Usuario { get; private set; }
    public DateTime DataLancamento { get; private set; }
    public DateOnly DataFabricacao { get; private set; }
    public DateOnly DataValidade { get; private set; }
    public int NumeroNf { get; private set; }
    public int SerieNf { get; private set; }
    public SituacaoLote Situacao { get; private set; }
    public virtual LoteInformacaoTecnica LoteInformacaoTecnica { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void AtualizarDataFabricacao(DateOnly dataFabricacao)
    {
        DataFabricacao = dataFabricacao;
    }

    public void AtualizarDataValidade(DateOnly dataValidade)
    {
        DataValidade = dataValidade;
        ValidarDataValidade();
    }

    public void ValidarDataValidade()
    {
        if (DataValidade < DateTime.UtcNow.Date.ToDateOnly())
            throw new DomainException(
                Localizer.Instance.GetMessage_Lote_DataValidadeVencida());
    }

    public void AtualizarSituacao(SituacaoLote situacao)
    {
        if (situacao == Situacao) return;

        Situacao = situacao;
    }

    public void AtualizarOperadorId(Guid usuarioId)
    {
        UsuarioId = usuarioId;
    }

    public override void OnAdded()
    {
        base.OnAdded();
        DomainEvent.RaiseExternal(new LoteCadastradoEvent(Id, Numero));
    }

    public override void Remove()
    {
        base.Remove();

        DomainEvent.RaiseExternal(new LoteRemovidoEvent(Numero, Id));
    }

    public LoteInformacaoTecnica AtualizarLoteInformacaoTecnica(Guid? paisId, Lote loteOrigem,
        LoteInformacaoTecnicaDto loteInformacaoTecnicaDto)
    {
        if (LoteInformacaoTecnica is not null)
        {
            LoteInformacaoTecnica.AtualizarLoteInformacaoTecnica(loteInformacaoTecnicaDto, paisId, loteOrigem, Numero);
        }
        else
        {
            LoteInformacaoTecnica = new LoteInformacaoTecnica(Id,
                paisId,
                loteOrigem?.Id,
                loteInformacaoTecnicaDto);

            LoteInformacaoTecnica.ValidarDensidade(loteInformacaoTecnicaDto.Densidade, Numero);
        }

        return LoteInformacaoTecnica;
    }
}