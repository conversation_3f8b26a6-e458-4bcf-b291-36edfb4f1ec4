using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate.Events;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Estoque.Resources;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Events;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;

public class LocalEstoque : Entity, IAggregateRoot, ITenant
{
    public LocalEstoque()
    {
    }

    public LocalEstoque(string descricao,
        TipoEstoque tipoEstoque,
        Empresa empresa)
    {
        Descricao = descricao;
        TipoEstoque = tipoEstoque;
        Empresa = empresa;
        Ativo = true;
    }

    public LocalEstoque(string descricao,
        bool ativo,
        TipoEstoque tipoEstoque,
        Empresa empresa,
        Guid tenantId,
        Guid groupTenantId) : this(descricao, tipoEstoque, empresa)
    {
        TenantId = tenantId;
        GroupTenantId = groupTenantId;
    }

    public Guid EmpresaId { get; set; }
    public string Descricao { get; private set; }
    public bool Ativo { get; private set; }
    public TipoEstoque TipoEstoque { get; private set; }
    public int SequenciaGroupTenant { get; private set; }

    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void AtualizarLocalEstoque(string descricao, TipoEstoque tipoEstoque, Empresa empresa)
    {
        Descricao = descricao;
        TipoEstoque = tipoEstoque;
        Empresa = empresa;
    }

    public void AtualizarStatus(bool ativo)
    {
        Ativo = ativo;
    }

    public override void Remove()
    {
        base.Remove();

        DomainEvent.RaiseExternal(new LocalEstoqueRemovidoEvent(this));
    }

    public override void OnAdded()
    {
        DomainEvent.RaiseExternal(new LocalEstoqueCadastradoEvent(Id, TenantId, GroupTenantId,
            Empresa.Id, Descricao));
        base.OnAdded();
    }

    public void ValidarEmpresaSePertenceAoLocalEstoque(Empresa empresa)
    {
        if (Empresa != empresa)
            throw new ValidationException(nameof(Id),
                Localizer.Instance.GetMessage_LocalEstoque_DevePertencerAMesmaEmpresa(Id));
    }

    #region Navigation properties

    public virtual Empresa Empresa { get; set; }

    public virtual ICollection<SaldoEstoque> SaldosEstoque { get; set; } = new List<SaldoEstoque>();

    #endregion
}