using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Shared.Domain.SeedWork;
using Bootis.Venda.Domain.AggregatesModel.StatusAtendimentoAggregate;

namespace Bootis.Venda.Domain.AggregatesModel.AtendimentoAggregate;

public class Atendimento : Entity, IAggregateRoot, ITenant
{
    public Atendimento()
    {
    }

    public Atendimento(Cliente cliente,
        Guid canalAtendimentoId,
        Usuario atendente,
        StatusAtendimento statusAtendimento)
    {
        Cliente = cliente;
        CanalAtendimentoId = canalAtendimentoId;
        Atendente = atendente;
        StatusAtendimento = statusAtendimento;
        DataCriacao = DateTime.UtcNow;
        DataAtualizacao = DateTime.UtcNow;
    }

    public int SequenciaGroupTenant { get; private set; }
    public Guid ClienteId { get; set; }
    public Guid AtendenteId { get; set; }
    public Guid StatusAtendimentoId { get; set; }
    public Guid CanalAtendimentoId { get; private set; }
    public string IdentificadorCanal { get; private set; }
    public DateTime DataCriacao { get; private set; }
    public DateTime DataAtualizacao { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void AtualizarStatus(Guid statusAtendimentoId)
    {
        if (StatusAtendimentoId == statusAtendimentoId) return;

        StatusAtendimentoId = statusAtendimentoId;
        DataAtualizacao = DateTime.UtcNow;
    }

    public void DefinirIdentificador(CanalAtendimento canal, Cliente cliente)
    {
        if (canal.Icon == "counter")
        {
            IdentificadorCanal = "Balcão";
            return;
        }

        var contato = cliente.Contatos
            .FirstOrDefault(c => c.TipoContato.Icon == canal.Icon);

        IdentificadorCanal = contato?.Identificacao;
    }
    
    #region Navigation properties

    public virtual Cliente Cliente { get; set; }
    public virtual Usuario Atendente { get; set; }
    public virtual StatusAtendimento StatusAtendimento { get; set; }
    public virtual CanalAtendimento CanalAtendimento { get; set; }

    #endregion
}