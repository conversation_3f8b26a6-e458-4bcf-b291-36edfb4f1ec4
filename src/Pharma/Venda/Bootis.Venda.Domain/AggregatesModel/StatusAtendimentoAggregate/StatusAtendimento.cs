using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Domain.SeedWork;
using Bootis.Venda.Domain.AggregatesModel.AtendimentoAggregate;

namespace Bootis.Venda.Domain.AggregatesModel.StatusAtendimentoAggregate;

public class StatusAtendimento : Entity, IAggregateRoot, ITenant
{
    public StatusAtendimento()
    {
    }

    public StatusAtendimento(string descricao,
        bool ativo,
        int ordem,
        Usuario usuario)
    {
        Descricao = descricao;
        Ativo = ativo;
        Ordem = ordem;
        SetUsuario(usuario);
    }

    public string Descricao { get; private set; }
    public bool Ativo { get; private set; }
    public int Ordem { get; private set; }

    public string CorFonte { get; set; }
    public string CorFundo { get; set; }

    public Guid UsuarioId { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    private void SetUsuario(Usuario usuario)
    {
        Usuario = usuario;
    }

    public void AtualizarDescricao(string descricao)
    {
        if (Descricao == descricao) return;

        Descricao = descricao;
    }

    public void AtualizarAtivo(bool ativo)
    {
        if (Ativo == ativo) return;

        Ativo = ativo;
    }

    public void AtualizarOrdem(int ordem)
    {
        if (Ordem == ordem) return;

        Ordem = ordem;
    }

    #region Navigation properties

    public virtual Usuario Usuario { get; set; }
    public virtual ICollection<Atendimento> Atendimentos { get; set; } = new List<Atendimento>();

    #endregion
}