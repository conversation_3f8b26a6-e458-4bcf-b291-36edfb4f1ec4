using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Application.Mappers;
using Bootis.Organizacional.Application.Requests.Conglomerado.Cadastrar;
using Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaPagadoraAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Organizacional.Application.UseCases.Conglomerado;

public class CadastrarFilialRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IConglomeradoRepository conglomeradoRepository,
    IUsuarioRepository usuarioRepository,
    IEmpresaRepository empresaRepository)
    : IRequestHandler<CadastrarFilialRequest>
{
    public async Task Handle(CadastrarFilialRequest request, CancellationToken cancellationToken)
    {
        var conglomerado = await conglomeradoRepository.ObterConglomeradoAsync(request.ConglomeradoId);
        var endereco = EnderecoMapper.EnderecoFrom(request.Endereco);
        var usuario = await UsuarioResponsavelAsync(request);

        await empresaRepository.ValidarEmpresaAsync(request.Cnpj);

        var empresa = conglomerado.AdicionarFilial(request.RazaoSocial, request.NomeFantasia, request.Cnpj,
            endereco, usuario, request.Cnae, request.InscricaoEstadual, request.InscricaoMunicipal,
            request.Email, request.Site, request.Telefone);

        userContext.UserSession = userContext.UserSession with
        {
            UserIdentity = userContext.UserSession.UserIdentity with
            {
                UserId = usuario.Id,
                TenantId = empresa.Id,
                GroupTenantId = conglomerado.Id
            }
        };

        var empresaPagadora = await CadastrarEmpresaPagadoraAsync(request, empresa);

        empresa.AtualizarResponsavel(usuario);
        empresa.AdicionarEmpresaPagadora(empresaPagadora);

        conglomeradoRepository.Update(conglomerado);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private async Task<Domain.AggregatesModel.UsuarioAggregate.Usuario> UsuarioResponsavelAsync(
        CadastrarFilialRequest request)
    {
        if (request.UsuarioResponsavelId != null)
        {
            var usuarioResposanvel =
                await usuarioRepository.ObterUsuarioAsync(request.UsuarioResponsavelId.Value);

            usuarioResposanvel.AtualizarTipo(TipoUsuario.Master.Id);

            return usuarioResposanvel;
        }

        await usuarioRepository.ValidarUsuarioEmailAsync(request.Usuario.Email);

        var usuario = new Domain.AggregatesModel.UsuarioAggregate.Usuario(request.Usuario.Nome,
            request.Usuario.Sobrenome, request.Usuario.Email, TipoUsuario.Master.Id, request.Usuario.DataNascimento,
            request.Usuario.Cpf, request.Usuario.Celular);

        return usuario;
    }

    private async Task<EmpresaPagadora> CadastrarEmpresaPagadoraAsync(CadastrarFilialRequest request,
        Domain.AggregatesModel.EmpresaAggregate.Empresa matriz)
    {
        if (request.EmpresaPagadoraId.HasValue)
        {
            await empresaRepository.ValidarEmpresaAsync(request.EmpresaPagadoraId.Value);
            
            var empresaPagadora = new EmpresaPagadora(request.EmpresaPagadoraId.Value);

            empresaRepository.Add(empresaPagadora);

            return empresaPagadora;
        }

        var novaEmpresaPagadora = new EmpresaPagadora(matriz.Id);

        empresaRepository.Add(novaEmpresaPagadora);

        return novaEmpresaPagadora;
    }
}