using System.Data;
using Bootis.Organizacional.Application.Dtos;
using Bootis.Organizacional.Application.Helpers;
using Bootis.Organizacional.Application.Requests.Usuario.Obter;
using Bootis.Organizacional.Application.Services;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Organizacional.Domain.Statics;
using Bootis.Organizacional.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Infrastructure.Extensions;
using MediatR;

namespace Bootis.Organizacional.Infrastructure.Queries.Usuario.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection,
    IPermissaoAppService permissaoAppService)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                              SELECT DISTINCT u.id AS id,
                                           u.id AS id,
                                           u.nome AS nome,
                                           u.sobrenome AS sobrenome,
                                           u.cpf AS cpf,
                                           u.data_nascimento AS data_nascimento,
                                           u.email AS email,
                                           u.ativo AS ativo,
                                           u.email_alternativo AS email_alternativo,
                                           u.tenant_id AS tenant_id,
                                           t.numero AS celular,
                                           ag.id AS grupo_id,
                                           ag.nome AS nome_grupo,
                                           pu.permissao_id AS permissao_usuario_id,
                                           pu.ativo AS permissao_usuario_ativa,
                                           gp.permissao_id AS grupo_permissao_id,
                                           r.tema_usuario AS tema_usuario,
                                           r.texto_negrito AS texto_negrito,
                                           r.texto_ampliado AS texto_ampliado,
                                           r.contraste_aumentado AS contraste_aumentado,
                                           r.time_zone AS time_zone,
                                           r.idioma AS idioma,
                                           r.padrao_data AS padrao_data,
                                           r.padrao_hora AS padrao_hora
                           FROM usuarios u
                                    LEFT JOIN telefone_usuario t ON t.id = u.id
                                    LEFT JOIN usuarios_grupos ug ON ug.usuarios_id = u.id
                                    LEFT JOIN grupos ag ON ag.id = ug.grupos_id
                                    LEFT JOIN permissoes_usuarios pu ON pu.usuario_id = u.id
                                    LEFT JOIN preferencias_usuario r ON r.id = u.id
                                    LEFT JOIN grupos_permissoes gp ON gp.grupo_id = ug.grupos_id 
                                        AND NOT EXISTS (SELECT 1
                                                        FROM permissoes_usuarios
                                                        WHERE permissao_id = gp.permissao_id
                                                          AND usuario_id = u.id)
                           WHERE u.id = @id 
                             AND u.tenant_id = @tenantId
                           """;

        var result =
            await connection.QueryListAsync<QueryRespone>(sql,
                new { request.Id, userContext.TenantId });

        if (result.Count != 0)
            return MapToObterUsuarioResponse(result);

        var message = Localizer.Instance.GetMessage_Usuario_GuidNaoEncontrado(request.Id);
        throw new DomainException(message);
    }

    private ObterResponse MapToObterUsuarioResponse(List<QueryRespone> result)
    {
        var usuario = new ObterResponse
        {
            Id = result[0].Id,
            Sobrenome = result[0].Sobrenome,
            Cpf = result[0].Cpf,
            DataNascimento = result[0].DataNascimento == DateTime.MinValue ? null : result[0].DataNascimento,
            Email = result[0].Email,
            Ativo = result[0].Ativo,
            EmailAlternativo = result[0].EmailAlternativo,
            Nome = result[0].Nome,
            Celular = result[0].Celular,
            Modulos = GetModulosPermissoes(),
            Grupos = new List<ObterResponse.Grupo>(),
            ModulosSistema = SystemModulesHelper.GetSystemModule(result[0].TenantId)
        };

        var permissoes = usuario.Modulos
            .SelectMany(c => c.Itens.SelectMany(i => i.Itens))
            .ToArray();

        usuario.Preferencias = new PreferenciaUsuarioBaseDto
        {
            TemaUsuario = result[0].TemaUsuario,
            TextoNegrito = result[0].TextoNegrito,
            TextoAmpliado = result[0].TextoAmpliado,
            ContrasteAumentado = result[0].ContrasteAumentado,
            TimeZone = result[0].TimeZone,
            Idioma = result[0].Idioma,
            PadraoData = result[0].PadraoData,
            PadraoHora = result[0].PadraoHora
        };

        foreach (var item in result)
        {
            if (item.GrupoId != null && usuario.Grupos.All(x => x.GrupoId != item.GrupoId))
                usuario.Grupos.Add(new ObterResponse.Grupo
                {
                    NomeGrupo = item.NomeGrupo,
                    GrupoId = item.GrupoId
                });

            SetPermissaoUsuario(item, permissoes);
            SetPermissaoGrupo(item, permissoes);
        }

        return usuario;
    }

    private static void SetPermissaoUsuario(QueryRespone item, PermissaoUsuarioDto[] permissoes)
    {
        if (item.PermissaoUsuarioId is null) return;
        var permissao = PermissoesStatic.GetPermissao(item.PermissaoUsuarioId.Value);
        if (permissao is null) return;
        var permissaoUsuario = permissoes.FirstOrDefault(c => c.PermissaoId == permissao.Id);
        if (permissaoUsuario is null) return;
        permissaoUsuario.Ativo = item.PermissaoUsuarioAtiva;
        permissaoUsuario.Herdado = false;
    }

    private static void SetPermissaoGrupo(QueryRespone item, PermissaoUsuarioDto[] permissoes)
    {
        if (item.GrupoPermissaoId is null) return;
        var grupo = PermissoesStatic.GetPermissao(item.GrupoPermissaoId.Value);
        if (grupo is null) return;
        var permissao = permissoes.FirstOrDefault(c => c.PermissaoId == grupo.Id);
        if (permissao is null) return;
        permissao.Ativo = true;
        permissao.AddGrupo(item.NomeGrupo);
    }

    private ModuloUsuarioDto[] GetModulosPermissoes()
    {
        var modulos = permissaoAppService.ListarPermissoes();

        return modulos
            .Select(m => new ModuloUsuarioDto(m))
            .ToArray();
    }

    private record QueryRespone
    {
        public Guid Id { get; set; }
        public string Nome { get; init; }
        public string Sobrenome { get; init; }
        public string Cpf { get; init; }
        public DateTime DataNascimento { get; init; }
        public string Email { get; init; }
        public bool Ativo { get; init; }
        public string EmailAlternativo { get; init; }
        public Guid TenantId { get; set; }
        public string Celular { get; init; }
        public Guid? GrupoId { get; set; }
        public string NomeGrupo { get; init; }
        public int? PermissaoUsuarioId { get; init; }
        public bool PermissaoUsuarioAtiva { get; init; }
        public int? GrupoPermissaoId { get; init; }
        public TipoTemaUsuario TemaUsuario { get; init; }
        public bool TextoNegrito { get; init; }
        public bool TextoAmpliado { get; init; }
        public bool ContrasteAumentado { get; init; }
        public string TimeZone { get; init; }
        public string Idioma { get; init; }
        public string PadraoData { get; init; }
        public string PadraoHora { get; init; }
    }
}