using System.Data;
using Bootis.Organizacional.Application.Requests.Empresa.Obter;
using Bootis.Organizacional.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Organizacional.Infrastructure.Queries.Empresa.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT e.id AS id,
                                  e.razao_social AS razao_social,
                                  e.nome_fantasia AS nome_fantasia,
                                  e.inscricao_estadual AS inscricao_estadual,
                                  e.inscricao_municipal AS inscricao_municipal,
                                  e.cnpj AS cnpj,
                                  e.cnae AS cnae,
                                  e.ativa AS ativa,
                                  u.nome AS usuario_nome,
                                  u.sobrenome AS usuario_sobrenome,
                                  u.id AS usuario_responsavel_id,
                                  u.email AS usuario_email,
                                  ee.id AS endereco_id,
                                  ee.cep AS endereco_cep,
                                  ee.logradouro AS endereco_logradouro,
                                  ee.complemento AS endereco_complemento,
                                  ee.numero AS endereco_numero,
                                  ee.bairro AS endereco_bairro,
                                  ee.cidade AS endereco_cidade,
                                  ee.estado AS endereco_estado,
                                  t.tipo_id AS telefone_tipo_id,
                                  t.numero AS telefone_numero,
                                  c.tipo_moeda_id AS tipo_moeda_id,
                                  ae.id AS empresa_pagadora_id,
                                  e.email AS email,
                                  e.site AS site,
                                  ac.nome AS nome_conglomerado,
                                  ac.id AS conglomerado_id,
                                  e.telefone AS telefone,
                                  e.tipo_id AS tipo_id
                             FROM empresas e
                                  LEFT JOIN empresa_responsavel er ON er.id = e.id
                                  LEFT JOIN usuarios u ON er.usuario_id = u.id
                                  LEFT JOIN endereco_empresa ee ON ee.id = e.id
                                  LEFT JOIN telefones_empresa t ON t.empresa_id = e.id
                                  LEFT JOIN configuracoes_empresa c ON c.configuracao_id = e.id
                                  LEFT JOIN conglomerados ac ON e.conglomerado_id = ac.id
                                  LEFT JOIN empresas ae ON e.empresa_pagadora_id = ae.id
                            WHERE e.id = @id
                              AND ac.id = @groupTenantId
                           """;

        var result = await connection.QueryFirstOrDefaultAsync<QueryRespone>(sql,
            new { request.Id, userContext.GroupTenantId });

        if (result is not null)
            return MapToObterEmpresaResponse(result);

        var message = Localizer.Instance.GetMessage_Empresa_GuidNaoEncontrado(request.Id);
        throw new DomainException(message);
    }

    private static ObterResponse MapToObterEmpresaResponse(QueryRespone result)
    {
        var empresa = new ObterResponse
        {
            Id = result.Id,
            RazaoSocial = result.RazaoSocial,
            NomeFantasia = result.NomeFantasia,
            InscricaoEstadual = result.InscricaoEstadual,
            InscricaoMunicipal = result.InscricaoMunicipal,
            Cnae = result.Cnae,
            Cnpj = result.Cnpj,
            TipoMoedaId = result.TipoMoedaId,
            EmpresaPagadoraId = result.EmpresaPagadoraId,
            Email = result.Email,
            Site = result.Site,
            Ativa = result.Ativa,
            NomeConglomerado = result.Nome,
            ConglomeradoId = result.ConglomeradoId,
            TipoId = result.TipoId,
            Usuario = new ObterResponse.UsuarioResponse
            {
                Nome = result.UsuarioNome,
                Email = result.UsuarioEmail,
                Sobrenome = result.UsuarioSobrenome,
                UsuarioResposavelId = result.UsuarioResponsavelId
            },
            Telefone = result.Telefone
        };

        if (result.EnderecoId != null)
            empresa.Endereco = new ObterResponse.EnderecoResponse
            {
                Cep = result.EnderecoCep,
                Numero = result.EnderecoNumero,
                Bairro = result.EnderecoBairro,
                Cidade = result.EnderecoCidade,
                Complemento = result.EnderecoComplemento,
                Estado = result.EnderecoEstado,
                Logradouro = result.EnderecoLogradouro
            };

        return empresa;
    }

    private record QueryRespone
    {
        public Guid Id { get; set; }
        public string RazaoSocial { get; init; }
        public string NomeFantasia { get; init; }
        public string Nome { get; init; }
        public string InscricaoEstadual { get; init; }
        public string InscricaoMunicipal { get; init; }
        public string Cnpj { get; init; }
        public string Cnae { get; init; }
        public bool Ativa { get; init; }
        public string UsuarioNome { get; init; }
        public string UsuarioSobrenome { get; init; }
        public Guid UsuarioResponsavelId { get; set; }
        public string UsuarioEmail { get; init; }
        public Guid? EnderecoId { get; init; }
        public string EnderecoCep { get; init; }
        public string EnderecoLogradouro { get; init; }
        public string EnderecoComplemento { get; init; }
        public int EnderecoNumero { get; init; }
        public string EnderecoBairro { get; init; }
        public string EnderecoCidade { get; init; }
        public string EnderecoEstado { get; init; }
        public string TelefoneTipoId { get; init; }
        public string TelefoneNumero { get; init; }
        public int? TipoMoedaId { get; init; }
        public Guid? EmpresaPagadoraId { get; set; }
        public string Email { get; init; }
        public string Site { get; init; }
        public string NomeConglomerado { get; init; }
        public Guid ConglomeradoId { get; set; }
        public string Telefone { get; init; }
        public int TipoId { get; init; }
    }
}