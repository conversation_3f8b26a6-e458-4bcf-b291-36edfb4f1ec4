using System.Data;
using System.Reflection;
using System.Runtime.Serialization;
using Bootis.Organizacional.Application.Dtos;
using Bootis.Organizacional.Application.Requests.Grupo.Obter;
using Bootis.Organizacional.Application.Services;
using Bootis.Organizacional.Domain.Statics;
using Bootis.Organizacional.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Infrastructure.Extensions;
using MediatR;

namespace Bootis.Organizacional.Infrastructure.Queries.Grupo.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection,
    IPermissaoAppService permissaoAppService)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT g.id AS id,
                                  g.nome AS nome,
                                  g.descricao AS descricao,
                                  g.se_ativo AS se_ativo,
                                  p.permissao_id AS permissao_id
                             FROM grupos g
                                  LEFT JOIN grupos_permissoes p ON 
                                        g.id = p.grupo_id
                            WHERE g.id = @id 
                              AND g.group_tenant_id = @groupTenantId
                           """;

        var result = await connection.QueryListAsync<QueryResponse>(sql,
            new { request.Id, userContext.GroupTenantId });

        if (result.Count != 0)
            return MapToObterGrupoResponse(result);

        var message = Localizer.Instance.GetMessage_Grupo_GuidNaoEncontrado(request.Id);
        throw new DomainException(message);
    }

    private ObterResponse MapToObterGrupoResponse(List<QueryResponse> result)
    {
        var grupo = new ObterResponse
        {
            Id = result[0].Id,
            Nome = result[0].Nome,
            Descricao = result[0].Descricao,
            SeAtivo = result[0].SeAtivo,
            Modulos = GetModulosPermissoes()
        };

        var permissoes = grupo.Modulos
            .SelectMany(c => c.Itens
                .SelectMany(i => i.Itens));

        foreach (var permissaoGrupo in from item in result
                 where item.PermissaoId != null
                 select PermissoesStatic.GetPermissao(item.PermissaoId.Value)
                 into permissao
                 where permissao != null
                 let isIgnorada = typeof(PermissoesStatic)
                     .GetFields(BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic)
                     .FirstOrDefault(f => f.GetValue(null) == permissao)?
                     .IsDefined(typeof(IgnoreDataMemberAttribute), false) ?? false
                 where !isIgnorada
                 select permissoes.FirstOrDefault(c => c.PermissaoId == permissao.Id)
                 into permissaoGrupo
                 where permissaoGrupo != null
                 select permissaoGrupo)
            permissaoGrupo.Ativo = true;

        return grupo;
    }

    private ModuloGrupoDto[] GetModulosPermissoes()
    {
        var modulos = permissaoAppService.ListarPermissoes();

        return modulos
            .Select(m => new ModuloGrupoDto(m))
            .ToArray();
    }

    private record QueryResponse
    {
        public Guid Id { get; set; }
        public string Nome { get; init; }
        public string Descricao { get; init; }
        public bool SeAtivo { get; init; }
        public int? PermissaoId { get; init; }
    }
}