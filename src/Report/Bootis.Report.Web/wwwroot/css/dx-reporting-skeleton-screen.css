/**
* DevExpress HTML/JS Reporting (dist\css\dx-reporting-skeleton-screen.css)
* Version:  24.2.8
* Build date: Jun 17, 2025
* Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
* License: https://www.devexpress.com/Support/EULAs/universal.xml
*/
.dx-skeleton-screen-viewer:empty {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-image: linear-gradient(90deg, rgba(245, 245, 245, 0) 0%, rgba(245, 245, 245, 0.5) 50%, rgba(245, 245, 245, 0) 100%), /* toolbar items start */ radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), radial-gradient(#e5e5e5 20px, transparent 0), /* toolbar items end */ /* designer surface rows start */ radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), /* designer surface rows end */ /* page */ linear-gradient(white 100%, transparent 0), /* tabs */ radial-gradient(#7a7a7a 20px, transparent 0), radial-gradient(#7a7a7a 20px, transparent 0), radial-gradient(#7a7a7a 20px, transparent 0), radial-gradient(#7a7a7a 20px, transparent 0), /* tab panel */ linear-gradient(#606060 100%, transparent 0), /* toolbar background */ linear-gradient(#f5f5f5 100%, transparent 0), /* background */ linear-gradient(#e6e6e6 100%, transparent 0);
  background-size: 250px 100%, /* toolbar items start */ 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, 32px 32px, /* toolbar items end */ /* designer surface rows start */ 35% 12px, 35% 12px, 35% 12px, 28% 12px, 35% 12px, 35% 12px, 35% 12px, 28% 12px, 35% 12px, 35% 12px, 35% 12px, 28% 12px, 35% 12px, 35% 12px, 35% 12px, 28% 12px, 35% 12px, 35% 12px, 35% 12px, 28% 12px, 35% 12px, 35% 12px, 35% 12px, 28% 12px, /* designer surface rows end */ /* page */ 40% 100%, /* tabs */ 32px 32px, 32px 32px, 32px 32px, 32px 32px, /* tab panel */ 48px 100%, /* toolbar background */ 100% 70px, /* background */ 100% 100%;
  background-position: -50% 0, /* toolbar items start */ 30% 19px, calc(30% + 48px) 19px, calc(30% + 96px) 19px, calc(30% + 144px) 19px, calc(30% + 192px) 19px, calc(30% + 240px) 19px, calc(30% + 288px) 19px, calc(30% + 336px) 19px, calc(30% + 384px) 19px, calc(30% + 432px) 19px, calc(30% + 480px) 19px, calc(30% + 528px) 19px, calc(30% + 576px) 19px, calc(30% + 624px) 19px, calc(30% + 672px) 19px, /* toolbar items end */ /* designer surface rows start */ 50% 165px, 50% 185px, 50% 205px, 45% 225px, 50% 267px, 50% 287px, 50% 307px, 45% 327px, 50% 369px, 50% 389px, 50% 409px, 45% 429px, 50% 471px, 50% 491px, 50% 511px, 45% 531px, 50% 573px, 50% 593px, 50% 613px, 45% 633px, 50% 675px, 50% 695px, 50% 715px, 45% 735px, /* designer surface rows end */ /* page */ 50% 85px, /* tabs */ right 8px top 78px, right 8px top 118px, right 8px top 158px, right 8px top 198px, /* tab panel */ right top 70px, /* toolbar background */ 0 0, /* background */ 0 70px;
  animation: viewerloading 1.5s infinite;
}
@keyframes viewerloading {
  to {
    background-position: 150% 0, /* toolbar items start */ 30% 19px, calc(30% + 48px) 19px, calc(30% + 96px) 19px, calc(30% + 144px) 19px, calc(30% + 192px) 19px, calc(30% + 240px) 19px, calc(30% + 288px) 19px, calc(30% + 336px) 19px, calc(30% + 384px) 19px, calc(30% + 432px) 19px, calc(30% + 480px) 19px, calc(30% + 528px) 19px, calc(30% + 576px) 19px, calc(30% + 624px) 19px, calc(30% + 672px) 19px, /* toolbar items end */ /* designer surface rows start */ 50% 165px, 50% 185px, 50% 205px, 45% 225px, 50% 267px, 50% 287px, 50% 307px, 45% 327px, 50% 369px, 50% 389px, 50% 409px, 45% 429px, 50% 471px, 50% 491px, 50% 511px, 45% 531px, 50% 573px, 50% 593px, 50% 613px, 45% 633px, 50% 675px, 50% 695px, 50% 715px, 45% 735px, /* designer surface rows end */ /* page */ 50% 85px, /* tabs */ right 8px top 78px, right 8px top 118px, right 8px top 158px, right 8px top 198px, /* tab panel */ right top 70px, /* toolbar background */ 0 0, /* background */ 0 70px;
  }
}
.dx-skeleton-screen-designer:empty {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-image: linear-gradient(90deg, rgba(245, 245, 245, 0) 0%, rgba(245, 245, 245, 0.5) 50%, rgba(245, 245, 245, 0) 100%), /* toolbox items start */ radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), radial-gradient(#eeeeee 84px, transparent 0), linear-gradient(#e6e6e6 84px, transparent 0), linear-gradient(#e6e6e6 84px, transparent 0), linear-gradient(#e6e6e6 84px, transparent 0), /* toolbox items end */ /* toolbar items start */ radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#e5e5e5 84px, transparent 0), radial-gradient(#606060 84px, transparent 0), radial-gradient(#525252 84px, transparent 0), /* toolbar items end */ /* toolbar sub*/ radial-gradient(#e5e5e5 84px, transparent 0), linear-gradient(#f5f5f5 100%, transparent 0), /* properties group start*/ radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), /* properties group end */ /* property grid */ linear-gradient(white 100%, transparent 0), /* toolbox items */ linear-gradient(white 100%, transparent 0), /* designer surface rows start */ radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), /* designer surface rows end */ /* designer surface */ linear-gradient(white 100%, transparent 0), /* tabs */ radial-gradient(#7a7a7a 84px, transparent 0), radial-gradient(#7a7a7a 84px, transparent 0), radial-gradient(#7a7a7a 84px, transparent 0), radial-gradient(#7a7a7a 84px, transparent 0), /* tab panel */ linear-gradient(#606060 100%, transparent 0), /* background */ linear-gradient(#e6e6e6 100%, transparent 0);
  background-size: 250px 100%, /* toolbox items start */ 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 96px 1px, 96px 1px, 96px 1px, /* toolbox items end */ /* toolbar items start */ 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 120px 32px, 120px 32px, /* toolbar items end */ /* toolbar sub*/ 32px 32px, 100% 70px, /* properties group start*/ 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, /* properties group end */ /* property grid */ 342px 100%, /* toolbox items */ 96px 100%, /* designer surface rows start */ 40% 12px, 40% 12px, 40% 12px, 25% 12px, 40% 12px, 40% 12px, 40% 12px, 25% 12px, 40% 12px, 40% 12px, 40% 12px, 25% 12px, 40% 12px, 40% 12px, 40% 12px, 25% 12px, 40% 12px, 40% 12px, 40% 12px, 25% 12px, 40% 12px, 40% 12px, 40% 12px, 25% 12px, /* designer surface rows end */ /* designer surface*/ 50% 100%, /* tabs */ 32px 32px, 32px 32px, 32px 32px, 32px 32px, /* tab panel */ 48px 100%, /* background */ 100% 100%;
  background-position: -50% 0, /* toolbox items start */ 16px 85px, 16px 133px, 16px 181px, 16px 229px, 16px 290px, 16px 338px, 16px 404px, 16px 452px, 16px 500px, 16px 566px, 16px 614px, 16px 662px, 56px 85px, 56px 132px, 56px 181px, 56px 290px, 56px 404px, 56px 452px, 56px 566px, 56px 614px, 0 272px, 0 386px, 0 548px, /* toolbox items end */ /* toolbar items start */ calc(50% - 350px) 23px, calc(calc(50% - 350px) + 48px) 23px, calc(calc(50% - 350px) + 96px) 23px, calc(calc(50% - 350px) + 144px) 23px, calc(calc(50% - 350px) + 192px) 23px, calc(calc(50% - 350px) + 240px) 23px, calc(calc(50% - 350px) + 288px) 23px, calc(calc(50% - 350px) + 336px) 23px, calc(calc(50% - 350px) + 384px) 23px, calc(calc(50% - 350px) + 432px) 23px, calc(calc(50% - 350px) + 480px) 23px, calc(calc(50% - 350px) + 528px) 23px, calc(calc(50% - 350px) + 576px) 23px, right 15px top 19px, right 130px top 19px, /* toolbar items end */ /* toolbar sub*/ 16px 19px, 100% 0, /* properties group start*/ right 78px top 114px, right 178px top 134px, right 78px top 176px, right 178px top 196px, right 78px top 238px, right 178px top 258px, right 78px top 300px, right 178px top 320px, right 78px top 362px, right 178px top 382px, right 78px top 424px, right 178px top 444px, right 78px top 486px, right 178px top 506px, right 78px top 548px, right 178px top 568px, /* properties group end */ /* property grid */ right 48px top 70px, /* toolbox items */ 0 0, /* designer surface rows start */ 35% 188px, 35% 208px, 35% 228px, 28% 248px, 35% 290px, 35% 310px, 35% 330px, 28% 350px, 35% 392px, 35% 412px, 35% 432px, 28% 452px, 35% 494px, 35% 514px, 35% 534px, 28% 554px, 35% 596px, 35% 616px, 35% 636px, 28% 656px, 35% 698px, 35% 718px, 35% 738px, 28% 758px, /* designer surface rows end */ /* designer surface*/ 32% 108px, /* tabs */ right 8px top 78px, right 8px top 118px, right 8px top 158px, right 8px top 198px, /* tab panel */ right top 70px, /* background */ 0 0;
  animation: designerloading 1.5s infinite;
}
@keyframes designerloading {
  to {
    background-position: 150% 0, /* toolbox items start */ 16px 85px, 16px 133px, 16px 181px, 16px 229px, 16px 290px, 16px 338px, 16px 404px, 16px 452px, 16px 500px, 16px 566px, 16px 614px, 16px 662px, 56px 85px, 56px 133px, 56px 181px, 56px 290px, 56px 404px, 56px 452px, 56px 566px, 56px 614px, 0 272px, 0 386px, 0 548px, /* toolbox items end */ /* toolbar items start */ calc(50% - 350px) 23px, calc(calc(50% - 350px) + 48px) 23px, calc(calc(50% - 350px) + 96px) 23px, calc(calc(50% - 350px) + 144px) 23px, calc(calc(50% - 350px) + 192px) 23px, calc(calc(50% - 350px) + 240px) 23px, calc(calc(50% - 350px) + 288px) 23px, calc(calc(50% - 350px) + 336px) 23px, calc(calc(50% - 350px) + 384px) 23px, calc(calc(50% - 350px) + 432px) 23px, calc(calc(50% - 350px) + 480px) 23px, calc(calc(50% - 350px) + 528px) 23px, calc(calc(50% - 350px) + 576px) 23px, right 15px top 19px, right 130px top 19px, /* toolbar items end */ /* toolbar sub*/ 16px 19px, 100% 0, /* properties group start*/ right 78px top 114px, right 178px top 134px, right 78px top 176px, right 178px top 196px, right 78px top 238px, right 178px top 258px, right 78px top 300px, right 178px top 320px, right 78px top 362px, right 178px top 382px, right 78px top 424px, right 178px top 444px, right 78px top 486px, right 178px top 506px, right 78px top 548px, right 178px top 568px, /* properties group end */ /* property grid */ right 48px top 70px, /* toolbox items */ 0 0, /* designer surface rows start */ 35% 188px, 35% 208px, 35% 228px, 28% 248px, 35% 290px, 35% 310px, 35% 330px, 28% 350px, 35% 392px, 35% 412px, 35% 432px, 28% 452px, 35% 494px, 35% 514px, 35% 534px, 28% 554px, 35% 596px, 35% 616px, 35% 636px, 28% 656px, 35% 698px, 35% 718px, 35% 738px, 28% 758px, /* designer surface rows end */ /* designer surface*/ 32% 108px, right 8px top 78px, right 8px top 118px, right 8px top 158px, right 8px top 198px, /* tab panel */ right top 70px, /* background */ 0 0;
  }
}
.dx-skeleton-screen-mobile-viewer:empty {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-image: linear-gradient(90deg, rgba(245, 245, 245, 0) 0%, rgba(245, 245, 245, 0.5) 50%, rgba(245, 245, 245, 0) 100%), /* designer surface rows start */ radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), /* designer surface rows end */ /* background */ linear-gradient(white 100%, transparent 0);
  background-size: 250px 100%, /* designer surface rows start */ 90% 12px, 90% 12px, 90% 12px, 63% 12px, 90% 12px, 90% 12px, 90% 12px, 63% 12px, 90% 12px, 90% 12px, 90% 12px, 63% 12px, 90% 12px, 90% 12px, 90% 12px, 63% 12px, 90% 12px, 90% 12px, 90% 12px, 63% 12px, 90% 12px, 90% 12px, 90% 12px, 63% 12px, /* designer surface rows end */ /* background */ 100% 100%;
  background-position: -50% 0, /* designer surface rows start */ 50% 88px, 50% 108px, 50% 128px, 13% 148px, 50% 190px, 50% 210px, 50% 230px, 13% 250px, 50% 292px, 50% 312px, 50% 332px, 13% 352px, 50% 394px, 50% 414px, 50% 434px, 13% 454px, 50% 496px, 50% 516px, 50% 536px, 13% 556px, 50% 598px, 50% 618px, 50% 638px, 13% 658px, /* designer surface rows end */ /* background */ 0 0;
  animation: mobileviewerloading 1.5s infinite;
}
@keyframes mobileviewerloading {
  to {
    background-position: 150% 0, /* designer surface rows start */ 50% 88px, 50% 108px, 50% 128px, 13% 148px, 50% 190px, 50% 210px, 50% 230px, 13% 250px, 50% 292px, 50% 312px, 50% 332px, 13% 352px, 50% 394px, 50% 414px, 50% 434px, 13% 454px, 50% 496px, 50% 516px, 50% 536px, 13% 556px, 50% 598px, 50% 618px, 50% 638px, 13% 658px, /* designer surface rows end */ /* background */ 0 0;
  }
}
