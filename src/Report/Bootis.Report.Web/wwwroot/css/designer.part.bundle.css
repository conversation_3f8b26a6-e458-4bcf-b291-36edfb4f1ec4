/**
* DevExpress Analytics (dist\css\dx-querybuilder.css)
* Version:  24.2.8
* Build date: Jun 17, 2025
* Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
* License: https://www.devexpress.com/Support/EULAs/universal.xml
*/
.dxrd-control {
  border-width: 0px;
  border-style: none;
  background: none;
  position: absolute;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
}
.dxrd-control .dxrd-disabled {
  opacity: 0.3;
}
.dxrd-control-content-main {
  box-sizing: border-box;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: inline-block;
}
.dxdd-connecting-point {
  border-width: 0px;
  border-style: none;
  position: absolute;
  background: none;
}
.dxdd-connecting-point:hover line {
  stroke: #ff0000;
}
.dxdd-connection-line {
  width: 100%;
  height: 100%;
  overflow: visible;
}
.dxdd-connection-line line,
.dxdd-connection-line polyline {
  stroke-width: 1;
}
.dxdd-connection-line-draggable line,
.dxdd-connection-line-draggable polyline {
  stroke: inherit;
}
.dxdd-connector.dxd-state-selected line,
.dxdd-connector.dxd-state-selected polyline {
  stroke: inherit;
}
.inline-text-edit {
  width: 100%!important;
  height: 100%!important;
  margin: 0;
}
.inline-text-edit .dx-texteditor-input {
  background-color: white;
  color: black;
}
.dxdd-connector {
  border-width: 0px;
  border-style: none;
  background: none;
  position: absolute;
}
.dxdd-connector .dxrd-control {
  width: 7px;
  height: 7px;
  margin: -3px 0 0 -3px;
  background: none;
  border: 1px solid blue;
  z-index: 1;
}
.dxdd-connector .dxdd-connection-line {
  overflow: visible;
}
.dxrd-focused,
.dxrd-selected {
  z-index: 2;
}
.dxrd-focused .ui-resizable-handle,
.dxrd-selected .ui-resizable-handle {
  position: absolute;
  width: 5px;
  height: 5px;
  font-size: 1px;
  border: 1px solid #333;
  visibility: inherit;
}
.dxrd-focused .ui-resizable-nw,
.dxrd-selected .ui-resizable-nw {
  top: -8px;
  left: -8px;
  cursor: nw-resize;
}
.dxrd-focused .ui-resizable-n,
.dxrd-selected .ui-resizable-n {
  top: -8px;
  left: 50%;
  margin-left: -4px;
  cursor: n-resize;
}
.dxrd-focused .ui-resizable-ne,
.dxrd-selected .ui-resizable-ne {
  top: -8px;
  right: -8px;
  cursor: ne-resize;
}
.dxrd-focused .ui-resizable-w,
.dxrd-selected .ui-resizable-w {
  top: 50%;
  margin-top: -4px;
  left: -8px;
  cursor: w-resize;
}
.dxrd-focused .ui-resizable-e,
.dxrd-selected .ui-resizable-e {
  top: 50%;
  margin-top: -4px;
  right: -8px;
  cursor: e-resize;
}
.dxrd-focused .ui-resizable-sw,
.dxrd-selected .ui-resizable-sw {
  bottom: -8px;
  left: -8px;
  cursor: sw-resize;
}
.dxrd-focused .ui-resizable-s,
.dxrd-selected .ui-resizable-s {
  bottom: -8px;
  left: 50%;
  margin-left: -4px;
  cursor: s-resize;
}
.dxrd-focused .ui-resizable-se,
.dxrd-selected .ui-resizable-se {
  bottom: -8px;
  right: -8px;
  cursor: se-resize;
}

.dxrd-error-page {
  font-family: "Helvetica Neue", "Segoe UI", helvetica, verdana, sans-serif;
  background: #FFF0F0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  row-gap: 12px;
  height: 100%;
}
.dxrd-error-page-icon {
  width: 64px;
  height: 64px;
}
.dxrd-error-page-title {
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  color: #660000;
}
.dxrd-error-page-content {
  font-size: 16px;
  text-align: center;
  color: rgba(32, 32, 32, 0.87);
}
.dxqb-designer .dxqb-text-overflow-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.dxqb-designer .dxqb-table-resize-ghost.ui-resizable-resizing {
  position: absolute;
  z-index: 3;
  border: 1px solid #0000ff;
}
.dxqb-designer .dxqb-table {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  line-height: normal;
  width: 100%;
  height: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: default;
  border-bottom: none;
}
.dxqb-designer .dxqb-table .dx-checkbox-container {
  display: block;
}
.dxqb-designer .dxqb-table .dx-checkbox-container .dx-checkbox-icon {
  height: 12px;
  width: 12px;
  font-size: 8px;
  display: inline-block;
}
.dxqb-designer .dxqb-table .dxqb-table-loading .dxqb-table-loading-text {
  display: inline-block;
  line-height: 100px;
  width: 100%;
  overflow: hidden;
  text-align: center;
  font-size: 11px;
}
.dxqb-designer .dxqb-table .dxqb-table-head-field {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-table-checkbox-all {
  padding-bottom: 2px;
  margin-top: 12px;
  display: block;
  height: 100%;
}
.dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-table-title {
  height: 36px;
  line-height: 36px;
}
.dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-table-title > div {
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-table-title .dxqb-table-title-content {
  height: 36px;
  line-height: 36px;
  text-transform: uppercase;
  font-weight: bold;
}
.dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-federation-table-title {
  height: 48px;
  line-height: 48px;
}
.dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-federation-table-title .dxqb-federation-table-title-container {
  height: 24px;
}
.dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-federation-table-title .dxqb-federation-table-title-container .inline-text-edit .dx-texteditor-container {
  display: block;
}
.dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-federation-table-title .dxqb-federation-table-title-container .inline-text-edit .dx-texteditor-container .dx-texteditor-input-container {
  display: block;
}
.dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-federation-table-title .dxqb-table-subtitle {
  height: 24px;
  line-height: 24px;
}
.dxqb-designer .dxqb-table .dxqb-table-field {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  height: 32px;
  line-height: 32px;
  margin: 0 1px 0 1px;
  box-sizing: content-box;
  position: relative;
}
.dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-background {
  display: none;
  position: absolute;
  z-index: -1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  pointer-events: none;
}
.dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-checkbox-wrapper {
  display: block;
  height: 100%;
}
.dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-checkbox-wrapper .dxqb-table-field-checkbox {
  margin-top: 10px;
}
.dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-content {
  font-size: 11px;
  height: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-content .dxqb-table-field-state {
  display: block;
  height: 100%;
  padding-top: 1px;
}
.dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-content .dxqb-table-field-state .dxqb-image-field-state {
  display: inline-block;
  width: 12px;
  height: 12px;
  background-position: center;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  position: relative;
}
.dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-content .dxqb-table-field-state .dxqb-image-field-state svg {
  width: 12px;
  height: 12px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-content .dxqb-table-field-caption {
  cursor: default;
  display: block;
  height: 100%;
  overflow: hidden;
}
.dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-content .dxqb-table-field-caption .dxqb-table-field-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  display: block;
  height: 100%;
}
.dxqb-designer .dxqb-table .dxqb-table-field-selected-base {
  border-width: 1px;
  border-style: solid;
  margin: 0;
  margin-top: -1px;
  margin-bottom: 0px;
  box-sizing: content-box;
}
.dxqb-designer .dxqb-table .dxqb-table-field.dxd-state-joined {
  border-width: 1px;
  border-style: solid;
  margin: 0;
  margin-top: -1px;
  margin-bottom: 0px;
  box-sizing: content-box;
  border-color: inherit;
}
.dxqb-designer .dxqb-table .dxqb-table-field.dxd-state-joined .dxqb-table-field-background {
  opacity: 0.2;
  display: block;
}
.dxqb-designer .dxqb-table .dxqb-table-field.dxd-state-joined:hover .dxqb-table-field-background {
  opacity: 0.4;
  display: block;
}
.dxqb-designer .dxqb-table .dxqb-table-field.dxd-state-active {
  border-width: 1px;
  border-style: solid;
  margin: 0;
  margin-top: -1px;
  margin-bottom: 0px;
  box-sizing: content-box;
  border-color: inherit;
}
.dxqb-designer .dxqb-table .dxqb-table-field.dxd-state-active .dxqb-table-field-background {
  opacity: 0.8;
  display: block;
}
.dxqb-designer .dxqb-table .dxqb-table-field.dxd-state-active:hover .dxqb-table-field-background {
  opacity: 1;
  display: block;
}
.dxqb-designer .dxqb-table .dxqb-table-field.dxd-state-invalid {
  border-width: 1px;
  border-style: solid;
  margin: 0;
  margin-top: -1px;
  margin-bottom: 0px;
  box-sizing: content-box;
  border-color: inherit;
}
.dxqb-designer .dxqb-table .dxqb-table-field.dxd-state-invalid .dxd-qb-table-field-text-color {
  color: #d9534f;
}
.dxqb-designer .dxqb-table .dxqb-table-field.dxd-state-hovered {
  border-width: 1px;
  border-style: solid;
  margin: 0;
  margin-top: -1px;
  margin-bottom: 0px;
  box-sizing: content-box;
}
.dxqb-designer .dxqb-datamember-image {
  float: left;
  padding: 0;
  width: 16px;
  height: 30px;
  background-position: center center;
}
.dxqb-designer .dxqb-datamember-text {
  line-height: 30px;
  font-family: Helvetica, Arial, 'DejaVu Sans', 'Liberation Sans', Freesans, sans-serif;
  font-size: 11px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: #ffffff;
  margin-left: 7px;
  cursor: default;
}
.dxqb-designer .dxrd-right-panel {
  top: 0;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dx-editors {
  padding-top: 5px;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dx-field {
  padding: 0;
  background-color: transparent;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dx-field .dx-field-label {
  width: 25%;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dx-field .dx-field-value {
  width: 75%;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dxqb-selected-properties.dx-fieldset {
  padding: 0px 25px 10px 25px;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dxrd-treelist-search-panel-container {
  padding-right: 25px;
  padding-left: 25px;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dxqb-right-panel-fields {
  margin-top: 5px;
  overflow: hidden;
  max-height: 300px;
  width: 100%;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dxqb-right-panel-fields .dx-scrollable-wrapper {
  max-height: 300px;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dxqb-right-panel-fields .dx-scrollable-wrapper .dx-scrollable-container {
  max-height: 300px;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dxqb-right-panel-parameters {
  width: 100%;
  height: 100%;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dxqb-right-panel-parameters .dx-editors .dx-editor .dx-field-label.dx-accordion-header {
  position: relative;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dxqb-right-panel-search-box.dx-widget:not(.dx-scrollable-scrollbar):not(.dx-dropdowneditor-button):not(.dx-button-has-icon):not(.no-margin-right) {
  margin-left: 30px;
  margin-right: 25px;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dxqb-right-panel-search-box.dx-widget:not(.dx-scrollable-scrollbar):not(.dx-dropdowneditor-button):not(.dx-button-has-icon):not(.no-margin-right) .dx-texteditor-container {
  margin-left: 6px;
  padding-right: 6px;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dx-collectioneditor .dx-collectioneditor-header {
  margin-left: 30px;
  margin-right: 25px;
}
.dxqb-designer .dxrd-right-panel .dxrd-properties-wrapper .dx-collectioneditor .dx-collection-item {
  padding-left: 10px;
  padding-right: 25px;
}
.dxqb-designer.dx-editors .dxrd-properties-wrapper .dxrd-properties-grid {
  border-top-width: 0;
  margin-top: 0;
}
.dx-querybuilder .dxqb-designer .dxrd-right-panel {
  right: 0;
}
.dx-querybuilder .dxqb-designer .dxrd-surface-wrapper {
  left: 1px;
}
.dx-querybuilder .dxqb-designer .dxrd-surface-wrapper .dx-scrollable-content {
  height: 100%;
}
.dx-querybuilder .dxqb-designer .dxrd-surface-wrapper .dx-scrollable-content .dx-scrollview-content {
  height: 100%;
}
.dx-querybuilder .dxqb-designer .dxrd-toolbar-wrapper {
  left: 0;
}
.dx-querybuilder .dxqb-designer .dxqb-table {
  text-align: left;
}
.dx-querybuilder .dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-table-checkbox-all {
  float: left;
  padding-left: 1px;
  margin-left: 11px;
  margin-right: 4px;
}
.dx-querybuilder .dxqb-designer .dxqb-table .dxqb-table-head-field .dxqb-table-title {
  text-align: left;
  padding-left: 10px;
  padding-right: 5px;
}
.dx-querybuilder .dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-checkbox-wrapper {
  float: left;
}
.dx-querybuilder .dxqb-designer .dxqb-table .dxqb-table-field .dxqb-table-field-checkbox-wrapper .dxqb-table-field-checkbox {
  margin-left: 11px;
  margin-right: 14px;
}
.dx-querybuilder .dxqb-designer .dxqb-table .dxqb-table-field-content .dxqb-table-field-state {
  float: right;
  padding-right: 5px;
  box-sizing: border-box;
}
.dx-querybuilder .dxqb-designer .dxqb-table .dxqb-table-field-content .dxqb-table-field-caption {
  padding-right: 5px;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxrd-right-panel {
  left: 0;
  right: auto;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxrd-right-panel .dxrd-properties-wrapper .dxqb-right-panel-search-box.dx-widget:not(.dx-scrollable-scrollbar):not(.dx-dropdowneditor-button):not(.dx-button-has-icon):not(.no-margin-right) {
  margin-right: 30px;
  margin-left: 25px;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxrd-right-panel .dxrd-properties-wrapper .dxqb-right-panel-search-box.dx-widget:not(.dx-scrollable-scrollbar):not(.dx-dropdowneditor-button):not(.dx-button-has-icon):not(.no-margin-right) .dx-texteditor-container {
  margin-right: 6px;
  padding-left: 6px;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxrd-surface-wrapper {
  right: 1px;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxrd-surface {
  float: right;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxrd-toolbar-wrapper {
  right: 0;
  left: auto;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxqb-table {
  text-align: right;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxqb-table .dxqb-table-head-field .dxqb-table-checkbox-all {
  float: right;
  padding-right: 1px;
  margin-right: 11px;
  margin-left: 4px;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxqb-table .dxqb-table-head-field .dxqb-table-title {
  text-align: right;
  padding-right: 10px;
  padding-left: 5px;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxqb-table .dxqb-table-field .dxqb-table-field-checkbox-wrapper {
  float: right;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxqb-table .dxqb-table-field .dxqb-table-field-checkbox-wrapper .dxqb-table-field-checkbox {
  margin-right: 11px;
  margin-left: 14px;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxqb-table .dxqb-table-field-content .dxqb-table-field-state {
  float: left;
  padding-left: 5px;
}
.dx-querybuilder .dxqb-designer.dx-rtl .dxqb-table .dxqb-table-field-content .dxqb-table-field-caption {
  padding-left: 5px;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-toolbar-wrapper {
  z-index: 1;
  right: unset;
  min-width: 0;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-toolbar-wrapper .dxqb-toolbar-background {
  position: absolute;
  z-index: -1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  pointer-events: none;
  opacity: 0.5;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper {
  top: 1px;
  bottom: 1px;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-ghost-container {
  height: 100%;
  width: 100%;
  position: relative;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxqb-placeholder {
  position: relative;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
  text-align: center;
  overflow: hidden;
  margin: 20px;
  font-family: 'Helvetica Neue', 'Segoe UI', Helvetica, Verdana, san-serif;
  font-size: 24px;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor {
  z-index: 1;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor.dxd-state-joined,
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor.dxd-state-active {
  z-index: 2;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor:hover {
  z-index: 3;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor.dxrd-selected,
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor.dxrd-focused {
  z-index: 2;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor.dxrd-selected .ui-resizable-handle,
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor.dxrd-focused .ui-resizable-handle {
  border-width: 0px;
  border-style: none;
  top: 0;
  margin-top: 0;
  height: 100%;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor.dxrd-selected .ui-resizable-w,
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor.dxrd-focused .ui-resizable-w {
  left: -4px;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor.dxrd-selected .ui-resizable-e,
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxd-track-cursor.dxrd-focused .ui-resizable-e {
  right: -4px;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxqb-table-background {
  position: absolute;
  z-index: -1;
  top: -5px;
  bottom: -5px;
  left: -5px;
  right: -5px;
  pointer-events: none;
  display: none;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxqb-table-border {
  position: absolute;
  z-index: -1;
  top: -1px;
  bottom: -1px;
  left: -1px;
  right: -1px;
  pointer-events: none;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxrd-selected .dxqb-table-background {
  display: block;
  opacity: 0.2;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxqb-main .dxrd-selected .dx-background-inheritor .dxqb-table-border {
  border-color: inherit;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxdd-connection-line line,
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxdd-connection-line polyline {
  stroke-width: 1px;
  shape-rendering: crispEdges;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxdd-connection-line marker {
  fill: none;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxdd-connection-line marker polyline {
  fill: none;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxdd-connection-line-draggable {
  position: absolute;
  overflow: visible;
  z-index: 1;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxdd-connection-line-draggable line,
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxdd-connection-line-draggable polyline {
  stroke-width: 1px;
  shape-rendering: crispEdges;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxdd-connection-line-draggable marker {
  fill: none;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxdd-connection-line-draggable marker polyline {
  fill: none;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-selected.dxdd-connector .dxdd-connection-line line,
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-selected.dxdd-connector .dxdd-connection-line polyline {
  stroke-width: 3px;
}
.dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-selected.dxdd-connector .dxrd-control {
  border-width: 0px;
  border-style: none;
}
.dxqb-preview {
  font-family: Helvetica, Arial, 'DejaVu Sans', 'Liberation Sans', Freesans, sans-serif;
  font-size: 12px;
}
.dxqb-preview .dx-overlay-content {
  max-height: 95%;
  max-width: 95%;
  min-width: 450px;
  min-height: 330px;
}
.dxqb-preview .dxqb-preview-popup-content {
  height: 100%;
  padding-bottom: 76px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dxqb-preview .dxqb-show-query-string-content {
  height: 100%;
}
.dxqb-preview .dxqb-show-query-string-content .dxrd-show-query-string-editor {
  height: 100%;
  background: transparent;
  border-width: 0px;
  border-style: none;
}
.dxqb-preview .dxqb-show-query-string-content .dxrd-show-query-string-editor textarea {
  background: transparent;
  padding: 7px 21px 8px;
}
.dxqb-preview .dxqb-show-query-string-content .dxrd-show-query-string-editor .dxrd-show-query-string-editor-content {
  margin: 7px 21px 8px;
  height: 100%;
}
.dxqb-preview .dxqb-preview-popup-buttons {
  border-top-width: 1px;
  border-top-style: solid;
  bottom: 0;
  height: 69px;
  padding-top: 1px;
  position: absolute;
  width: 100%;
}
.dxqb-preview .dxqb-preview-popup-buttons .dxqb-preview-popup-button {
  text-align: center;
  margin: 15px;
  float: right;
  width: 97px;
}
.dx-rtl .dxqb-preview .dxqb-preview-popup-buttons .dxqb-preview-popup-button {
  float: left;
}
.dxqb-preview .dxd-tableview {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
.dxqb-preview .dxd-tableview .dxd-tableview-cell {
  border-left-width: 1px;
  border-left-style: solid;
}
.dx-rtl .dxqb-preview .dxd-tableview .dxd-tableview-cell {
  direction: rtl;
}
.dxqb-preview .dxd-tableview .dxd-tableview-cell .dxd-tableview-cell-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  text-align: left;
  vertical-align: top;
  padding: 7px;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}
.dx-rtl .dxqb-preview .dxd-tableview .dxd-tableview-cell .dxd-tableview-cell-text {
  text-align: right;
}
.dxqb-preview .dxd-tableview tr > .dxd-tableview-cell:first-child,
.dxqb-preview .dxd-tableview tr > .dxd-tableview-title-cell:first-child {
  border-left: 0;
}
.dxqb-preview .dxd-tableview tr {
  height: 30px;
}
.dxqb-preview .dxd-tableview .dxd-tableview-titles {
  position: relative;
  direction: ltr;
}
.dxqb-preview .dxd-tableview .dxd-tableview-titles .dxd-tableview-title-cell {
  border-left-width: 1px;
  border-left-style: solid;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.dx-rtl .dxqb-preview .dxd-tableview .dxd-tableview-titles .dxd-tableview-title-cell {
  direction: rtl;
}
.dxqb-preview .dxd-tableview .dxd-tableview-titles .dxd-tableview-title-cell .dxd-tableview-cell-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  text-align: left;
  vertical-align: top;
  padding: 7px;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}
.dx-rtl .dxqb-preview .dxd-tableview .dxd-tableview-titles .dxd-tableview-title-cell .dxd-tableview-cell-text {
  text-align: right;
}
.dxqb-preview .dxd-tableview .dxd-tableview-data {
  padding-bottom: 32px;
}
.dxqb-preview .dxd-tableview .dxd-tableview-data table {
  direction: ltr;
  min-height: 100%;
}
.dxqb-preview .dxd-tableview .dxd-tableview-data table .dxd-tableview-data-fakerow {
  height: auto;
}
.dxqb-preview .dxd-tableview .dxd-tableview-data table .dxd-tableview-data-fakerow .dxd-tableview-cell-text {
  padding: 0;
}
.dxqb-preview .dxd-tableview table {
  border-spacing: 0;
  min-width: 100%;
}
.dxqb-preview .dxd-tableview .dx-scrollable-content {
  height: 100%;
  padding: 0;
}
.dxqb-preview .dxd-tableview .dx-scrollable-content .dx-scrollview-content {
  height: 100%;
}
.dxqb-preview .dxd-tableview .dxd-tableview-empty-message {
  padding: 10px;
}
.dxqb-preview .dxd-tableview .dx-scrollbar-vertical.dx-scrollbar-hoverable {
  width: 8px;
}
.dxqb-preview .dxd-tableview .dx-scrollbar-vertical.dx-scrollbar-hoverable .dx-scrollable-scroll {
  width: 8px;
}
.dxqb-preview .dxd-tableview .dx-scrollbar-vertical.dx-scrollbar-hoverable.dx-state-hover,
.dxqb-preview .dxd-tableview .dx-scrollbar-vertical.dx-scrollbar-hoverable.dx-scrollable-scrollbar-active {
  width: 15px;
}
.dxqb-preview .dxd-tableview .dx-scrollbar-vertical.dx-scrollbar-hoverable.dx-state-hover .dx-scrollable-scroll,
.dxqb-preview .dxd-tableview .dx-scrollbar-vertical.dx-scrollbar-hoverable.dx-scrollable-scrollbar-active .dx-scrollable-scroll {
  width: 15px;
}
.dx-designer .dx-querybuilder .dxrd-designer-wrapper .dxrd-surface-wrapper:empty {
  background-image: none;
}
.dx-fullscreen-wizard {
  font-weight: normal;
  font-size: 14px;
  font-family: 'Helvetica Neue', 'Segoe UI', Helvetica, Verdana, sans-serif;
  line-height: 1.35715;
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1001;
}
.dx-ltr.dx-fullscreen-wizard {
  left: 50px;
  right: 0px;
}
.dx-rtl.dx-fullscreen-wizard {
  left: 0px;
  right: 50px;
}
.dx-designer .dx-fullscreen-wizard * {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
.dx-fullscreen-wizard .dxrd-wizard {
  width: 100%;
  height: 100%;
  position: relative;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-title {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  font-family: 'Helvetica Neue', 'Segoe UI', Helvetica, Verdana, san-serif;
  font-weight: 500;
  font-size: 13px;
  text-transform: uppercase;
  height: 60px;
  line-height: 60px;
  padding-right: 21px;
  padding-left: 21px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-disabled {
  opacity: 0.3;
  pointer-events: none;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile {
  position: absolute;
  overflow: hidden;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  position: absolute;
  width: 100%;
  height: 20px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dx-list .dx-list-search,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dx-list-with-search .dx-list-search {
  border-top: none;
  border-right: none;
  border-left: none;
  height: 36px;
  border-color: inherit;
  border-radius: 0;
  margin-bottom: 0;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dx-list.dx-state-focused,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dx-list-with-search.dx-state-focused,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dx-list.dx-state-hover,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dx-list-with-search.dx-state-hover {
  border-color: inherit;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content {
  position: absolute;
  width: 100%;
  top: 30px;
  bottom: 0px;
  height: auto;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page {
  margin: 0px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-disabled-content {
  margin: auto;
  font-size: 14px;
  height: 14px;
  line-height: 14px;
  text-align: center;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonsource-page {
  padding-top: 0px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-wizard-validationsummary {
  padding-right: 16px;
  padding-left: 14px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-wizard-dataMember {
  padding-top: 16px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-wizard-json-string-source-grid {
  position: absolute;
  width: 100%;
  bottom: 16px;
  top: 88px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-wizard-json-string-source-grid > div,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-wizard-json-string-source-grid .dx-editors,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-wizard-json-string-source-grid .dx-fieldset,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-wizard-json-string-source-grid .dx-field,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-wizard-json-string-source-grid .dxrd-jsonwizard-jsonstring-editor {
  height: 100%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-wizard-json-string-source-grid .dxrd-jsonwizard-jsonstring-editor.dx-textarea.dx-invalid {
  height: calc(100% - 40px);
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-add-queries-page .dxrd-treelist-search-panel,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-add-federation-queries-page .dxrd-treelist-search-panel {
  border-bottom: 0px;
  border-radius: 0px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-add-queries-page .dxrd-treelist-search-panel .dx-texteditor,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-add-federation-queries-page .dxrd-treelist-search-panel .dx-texteditor {
  border: 0px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonschema-page {
  padding-top: 0px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonschema-page .dxrd-wizard-dataMember {
  padding-top: 16px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dx-jsonschema-page .dxrd-wizard-dataMember .dxrd-wizard-add-queries-page {
  top: 60px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page .dxrd-wizard-list {
  height: 100%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page .dxrd-masterDetail-editor-complete-wizard {
  height: 100%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page .dxrd-wizard-dataMember-treelist {
  padding-top: 8px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dx-objectdatasource-types-section {
  height: 100%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dx-objectdatasource-types-section .dxrd-treelist-search-panel {
  border-radius: 0px;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
}
.dx-fullscreen-wizard .dxrd-wizard .dx-popup-normal {
  border-radius: 0px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container {
  width: 15%;
  max-width: 300px;
  height: 100%;
  min-width: 220px;
  display: inline-block;
  position: absolute;
}
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container {
  left: 0;
}
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container {
  right: 0;
}
.dx-designer-viewport-xs .dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container {
  display: none;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container .dxrd-wizard-steps-relative {
  position: relative;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container .dxrd-wizard-steps-relative .dxrd-wizard-steps-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  height: 50px;
  padding-top: 15px;
  font-size: 14px;
  box-sizing: border-box;
}
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container .dxrd-wizard-steps-relative .dxrd-wizard-steps-content {
  padding-right: 23px;
}
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container .dxrd-wizard-steps-relative .dxrd-wizard-steps-content {
  padding-left: 23px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container .dxrd-wizard-steps-relative .dxrd-wizard-steps-marker {
  width: 18px;
  height: 18px;
  position: absolute;
  top: 16px;
}
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container .dxrd-wizard-steps-relative .dxrd-wizard-steps-marker {
  left: -9px;
  transform: rotate(-45deg);
}
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-steps-container .dxrd-wizard-steps-relative .dxrd-wizard-steps-marker {
  right: -9px;
  transform: rotate(45deg);
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-navigation {
  padding: 0 30px 30px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content {
  width: calc(100% - 15%);
  max-width: calc(100% - 220px);
  height: 100%;
  display: inline-block;
  top: 0;
  right: 0;
  min-width: calc(100% - 300px);
}
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content:not(.withoutPanel) {
  right: 0;
  left: inherit;
}
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content:not(.withoutPanel) {
  left: 0;
  right: inherit;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content.withoutPanel {
  width: 100%;
  max-width: 100%;
  min-width: 100%;
}
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content.withoutPanel .dxrd-wizard-part-description-text {
  border-right-width: 1px;
  border-right-style: solid;
}
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content.withoutPanel .dxrd-wizard-part-description-text {
  border-left-width: 1px;
  border-left-style: solid;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-part-description {
  height: 60px;
  line-height: 60px;
  font-size: 16px;
}
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-part-description .dxrd-wizard-part-description-text {
  padding-right: 30px;
}
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-part-description .dxrd-wizard-part-description-text {
  padding-left: 30px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-work-content {
  width: 100%;
  height: calc(100% - 60px);
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-work-content-relative {
  position: relative;
  width: 100%;
  height: calc(100% - 60px);
}
.dx-designer-viewport-xs .dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content {
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  left: 0;
  right: 0;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-accordion-content.dx-fieldset {
  padding: 15px 0;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-editors .dx-editor .dx-field-label.dx-accordion-header {
  left: 42px;
}
.dxrd-wizard {
  cursor: default;
}
.dxrd-wizard .dx-popup-content {
  padding: 0;
}
.dxrd-wizard .dxrd-wizard-header-custom {
  height: 55px;
  margin-left: 1px;
  margin-right: 1px;
  margin-top: 8px;
}
.dxrd-wizard .dxrd-wizard-header-custom .dxrd-span-title {
  font-size: large;
}
.dxrd-wizard .dxrd-wizard-header-custom .dxrd-span-description {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  height: 20px;
}
.dxrd-wizard .dxrd-wizard-content {
  top: 76px;
  bottom: 77px;
  position: absolute;
  width: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dx-button {
  margin: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page {
  height: 100%;
  padding: 20px 21px 20px 21px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-wizard-connections {
  height: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-page-specify-connection_createNew {
  height: calc(100% - 65px);
  position: relative;
  top: 65px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-availabledatasources {
  position: absolute;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page {
  padding-top: 4px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-wizard-validationsummary {
  padding-right: 16px;
  padding-left: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxrd-jsonwizard-jsonstring-editor {
  min-height: 230px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxd-wizard-jsoneditor {
  border-width: 1px;
  border-style: solid;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxd-upload-file {
  z-index: 15;
  width: 28px;
  height: 28px;
  position: absolute;
  cursor: pointer;
  right: 25px;
  top: 13px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxd-upload-file > div {
  width: 100%;
  height: 100%;
  position: absolute;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxd-upload-file .dxd-back-primary2 {
  opacity: 0.75;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxd-upload-file:hover .dxd-back-primary2 {
  opacity: 1;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxd-upload-file:hover .dx-button {
  border-style: solid;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxd-upload-file .dx-button {
  background: transparent;
  border-style: none;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxd-upload-file .dx-button .dx-icon {
  width: 26px;
  height: 26px;
  background: transparent;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dxd-upload-file .dx-button .dx-icon > svg {
  width: 24px;
  height: 24px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-jsonwizard-parameter {
  position: absolute;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-texteditor {
  height: 36px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-texteditor .dx-texteditor-input {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-texteditor .dx-placeholder {
  font-size: 14px;
  margin-top: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-field {
  padding-top: 4px;
  padding-bottom: 4px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-field .dx-field-label:not(.dx-accordion-empty) {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-field .dx-field-value {
  width: 60%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-field .dx-field-value.dx-widget {
  height: 36px;
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-field .dx-group-header-font {
  font-size: 12px;
  display: inline-block;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-collectioneditor .dx-collectioneditor-items .dx-collectioneditor-item-container {
  height: 44px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-collectioneditor .dx-collectioneditor-items .dx-collectioneditor-item-container .dx-field {
  display: flex;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-jsonwizard-parameter-container {
  width: 33%;
  position: relative;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-jsonwizard-parameter-left-container {
  width: 33%;
  position: relative;
  float: left;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-jsonwizard-parameter-left-container .dx-jsonwizard-parameter {
  left: 16px;
  right: 2px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-jsonwizard-parameter-middle-container {
  width: 33%;
  position: relative;
  float: left;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-jsonwizard-parameter-middle-container .dx-jsonwizard-parameter {
  left: 2px;
  right: 2px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-jsonwizard-parameter-right-container {
  width: 33%;
  position: relative;
  float: right;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonsource-page .dx-jsonwizard-parameter-right-container .dx-jsonwizard-parameter {
  right: 0;
  left: 2px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonschema-page {
  padding-top: 4px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonschema-page .dx-field {
  padding-top: 4px;
  padding-bottom: 4px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonschema-page .dx-field .dx-field-label {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-jsonschema-page .dxrd-wizard-add-queries-page {
  position: absolute;
  top: 48px;
  bottom: 0px;
  width: 100%;
  height: auto;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-list-item,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-list .dx-empty-message {
  border-width: 0px;
  border-style: none;
  margin-bottom: 1px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-treelist-item {
  height: auto;
  padding: 5px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-list {
  height: 286px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-list .dx-list-item-before-bag {
  display: none;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-list .dx-list-search,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-list-with-search .dx-list-search {
  border-top: none;
  border-right: none;
  border-left: none;
  height: 36px;
  border-color: inherit;
  border-radius: 0;
  margin-bottom: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-list.dx-state-focused,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-list-with-search.dx-state-focused,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-list.dx-state-hover,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-list-with-search.dx-state-hover {
  border-color: inherit;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-list-title {
  margin-bottom: 10px;
}
.dxrd-multiqueries-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page,
.dxrd-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-frameless-style {
  padding: 0px;
}
.dxrd-multiqueries-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-datasourceoperation,
.dxrd-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-frameless-style .dxrd-wizard-datasourceoperation {
  padding: 8px 20px;
  position: absolute;
  height: 65px;
}
.dxrd-multiqueries-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-default-border-style,
.dxrd-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-frameless-style .dx-default-border-style,
.dxrd-multiqueries-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-dataMember,
.dxrd-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-frameless-style .dxrd-wizard-dataMember,
.dxrd-multiqueries-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-filtereditor,
.dxrd-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-frameless-style .dx-filtereditor {
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.dxrd-multiqueries-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-filtereditor-tree,
.dxrd-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-frameless-style .dx-filtereditor-tree {
  border-width: 0px;
  border-style: none;
  padding-left: 20px;
}
.dxrd-multiqueries-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-item-content.dx-list-item-content,
.dxrd-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page.dx-frameless-style .dx-item-content.dx-list-item-content {
  padding: 10px 20px;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page {
  height: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dxa-no-data-message,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dxa-no-data-message {
  text-align: center;
  position: absolute;
  top: 100px;
  width: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dxrd-treelist-search-panel,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dxrd-treelist-search-panel,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dx-list-search,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dx-list-search {
  border-top: none;
  border-right: none;
  border-left: none;
  height: 36px;
  border-color: inherit;
  border-radius: 0;
  margin-bottom: 0;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dxrd-treelist-search-panel .dx-texteditor-input,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dxrd-treelist-search-panel .dx-texteditor-input,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dx-list-search .dx-texteditor-input,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dx-list-search .dx-texteditor-input {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dxrd-treelist-search-panel .dx-placeholder,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dxrd-treelist-search-panel .dx-placeholder,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dx-list-search .dx-placeholder,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dx-list-search .dx-placeholder {
  font-size: 14px;
  margin-top: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dxrd-treelist-search-panel.dx-state-focused,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dxrd-treelist-search-panel.dx-state-focused,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dx-list-search.dx-state-focused,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dx-list-search.dx-state-focused,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dxrd-treelist-search-panel.dx-state-hover,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dxrd-treelist-search-panel.dx-state-hover,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-types-page .dx-list-search.dx-state-hover,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-datamembers-page .dx-list-search.dx-state-hover {
  border-color: inherit;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-datamembers-section {
  height: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-datamembers-section .dx-list-search {
  border-top: none;
  border-right: none;
  border-left: none;
  height: 36px;
  border-color: inherit;
  border-radius: 0;
  margin-bottom: 0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-datamembers-section .dx-list-search .dx-texteditor-input {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-datamembers-section .dx-list-search .dx-placeholder {
  font-size: 14px;
  margin-top: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-datamembers-section .dx-list-search.dx-state-focused,
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-datamembers-section .dx-list-search.dx-state-hover {
  border-color: inherit;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-section {
  height: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-section .dxrd-properties-wrapper {
  overflow: visible;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-section .dx-fieldset .dx-field .dx-field-label {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-section .dx-fieldset .dx-field .dx-field-value .dx-texteditor {
  height: 36px;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-section .dx-fieldset .dx-field .dx-field-value .dx-texteditor {
  margin-right: 40px;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-section .dx-fieldset .dx-field .dx-field-value .dx-texteditor {
  margin-left: 40px;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-section .dx-fieldset .dx-field .dx-field-value .dx-popup-general {
  margin-left: 0px;
  margin-right: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dx-objectdatasource-parameters-section .dx-fieldset .dx-field.dxd-back-primary .dx-field-label {
  font-weight: bold;
  width: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-editormenu-box {
  top: 0px;
  width: 36px;
  height: 36px;
  min-width: 36px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-editormenu-box .dx-icon {
  width: 16px;
  height: 16px;
  background-size: 16px;
  font-size: 16px;
  line-height: 16px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-editormenu-box .dx-icon svg {
  width: 16px;
  height: 16px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist-item .dx-treelist-caption .dx-treelist-action-container {
  padding-right: 6px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist-item .dx-treelist-caption .dx-treelist-selectedcontent {
  padding-left: 0px;
  padding-right: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist-item .dx-treelist-action,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist-item .dx-treelist-action-edit {
  vertical-align: top;
  margin-left: 6px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist-item .dx-treelist-text-with-checkbox .dx-queryname-textbox {
  height: 24px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist-item .dx-treelist-text-with-checkbox .dx-queryname-textbox .dx-texteditor-input {
  height: 24px;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist-item .dx-treelist-text-with-checkbox {
  padding-left: 31px;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist-item .dx-treelist-text-with-checkbox {
  padding-right: 31px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dx-radiogroup {
  margin-left: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dxrd-radio-nowrap-ellipsis {
  margin-top: 19px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dxrd-radio-nowrap-ellipsis .dx-radiobutton {
  table-layout: inherit;
  width: auto;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dx-editquery-editor {
  margin-top: 5px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dxrd-create-query-page-query-header {
  position: relative;
  height: 20px;
  margin-top: 19px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dxrd-create-query-page-query-header .dx-editquery-editor {
  margin-top: 0px;
  position: absolute;
  left: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dxrd-create-query-page-query-header .dxd-hyperlink-color {
  text-decoration: underline;
  cursor: pointer;
  position: absolute;
  right: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dxrd-create-query-page-editor {
  height: 182px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dxrd-create-query-page-editor.dxrd-wizard-list {
  height: 181px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dxrd-create-query-page-editor .dx-list-search {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-top: inherit;
  border-left: inherit;
  border-right: inherit;
  border-radius: inherit;
  margin-bottom: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dxrd-create-query-page-editor.dxrd-create-query-page-editor-border {
  border-width: 1px;
  border-style: solid;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-create-query-page .dxrd-create-query-page-content .dxrd-create-query-page-editor .dxrd-create-query-page-editor-content {
  padding: 7px 9px 8px;
  height: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-item-border-color {
  border-color: transparent;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page {
  display: -ms-flexbox;
  -ms-flex-wrap: wrap;
  -ms-flex-align: center;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
  align-items: center;
  justify-content: center;
  margin-top: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page .dxrd-wizard-type-item {
  border-width: 1px;
  border-style: solid;
  width: 120px;
  height: 150px;
  display: inline-block;
  margin: 0 10px;
  cursor: pointer;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page .dxrd-wizard-type-item .dxrd-wizard-type-image {
  width: 96px;
  height: 96px;
  margin: 15px auto 8px;
  position: relative;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page .dxrd-wizard-type-item .dxrd-wizard-type-image svg {
  width: 96px;
  height: 96px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page .dxrd-wizard-type-item .dxrd-wizard-type-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  text-align: center;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page .dxrd-wizard-type-item:hover,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page .dxrd-wizard-type-item.dxd-state-selected {
  border-width: 1px;
  border-style: solid;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page {
  flex-direction: column;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery.dx-list-with-search {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery.dx-list-with-search .dx-list-search {
  height: 36px;
  max-width: 820px;
  width: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery.dx-list-with-search .dx-list-search .dx-placeholder,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery.dx-list-with-search .dx-list-search input {
  font-size: 14px;
  margin-top: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery.dx-list-with-search .dx-scrollable-wrapper .dx-scrollable-content {
  align-content: flex-start;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery {
  margin-top: 54px;
  width: 100%;
  padding: 10px 10px 24px 10px;
}
.dxrd-wizard.dx-popup.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery {
  margin-top: 24px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery .dx-scrollable-wrapper .dx-scrollable-content {
  display: grid;
  align-content: center;
  min-width: 200px;
  justify-content: center;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery .dx-scrollable-wrapper .dx-scrollable-content .dx-scrollview-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-content: flex-start;
  margin-top: 20px;
  max-width: 900px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery .dx-scrollable-wrapper .dx-scrollable-content .dx-scrollview-content .dx-list-items {
  display: inherit;
  flex-wrap: wrap;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery .dx-scrollable-wrapper .dx-scrollable-content .dx-scrollview-content .dx-list-item,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery .dx-scrollable-wrapper .dx-scrollable-content .dx-scrollview-content .dx-empty-message {
  width: auto;
  border-top: 0;
  margin-bottom: 25px;
  margin-top: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery .dx-scrollable-wrapper .dx-scrollable-content .dx-scrollview-content .dx-list-item .dx-list-item-content,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery .dx-scrollable-wrapper .dx-scrollable-content .dx-scrollview-content .dx-empty-message .dx-list-item-content {
  padding: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery .dx-scrollable-wrapper .dx-scrollable-content .dx-scrollview-content .dx-list-item .dx-list-item-content .dxrd-report-type-item-text,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-type-page.dxrd-report-type-page .dxrd-wizard-report-gallery .dx-scrollable-wrapper .dx-scrollable-content .dx-scrollview-content .dx-empty-message .dx-list-item-content .dxrd-report-type-item-text {
  width: 100%;
  height: 100%;
  align-items: center;
  display: flex;
  white-space: break-spaces;
  text-align: center;
  font-size: 13px;
}
.dxrd-wizard .dxrd-wizard-navigation {
  bottom: 0;
  position: absolute;
  width: 100%;
}
.dxrd-wizard.dxrd-wizard .dxrd-wizard-navigation {
  padding: 24px;
  height: auto;
}
.dxrd-querybuilder-popup.dxrd-wizard .dxrd-wizard-navigation {
  border-top-width: 1px;
  border-top-style: solid;
  height: 55px;
}
.dxrd-wizard .dxrd-wizard-navigation .dxrd-wizard-btn {
  text-align: center;
  margin-top: 10px;
  min-width: 95px;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-navigation .dxrd-wizard-btn.left {
  float: left;
  margin-left: 0;
  margin-right: 8px;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-navigation .dxrd-wizard-btn.left {
  float: right;
  margin-right: 0;
  margin-left: 8px;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-navigation .dxrd-wizard-btn.right {
  float: right;
  margin-left: 10px;
  margin-right: 0;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-navigation .dxrd-wizard-btn.right {
  float: left;
  margin-right: 10px;
  margin-left: 0;
}
.dxrd-datasource-parameters {
  height: 100%;
  position: relative;
}
.dxrd-datasource-parameters .dxrd-collectioneditor-wizard-buttons {
  position: absolute;
  bottom: 24px;
}
.dxrd-datasource-parameters .dxrd-collectioneditor-action {
  width: auto;
  height: auto;
}
.dxrd-datasource-parameters .dxrd-datasource-parameters-header {
  line-height: 28px;
}
.dxrd-datasource-parameters .dxrd-datasource-parameters-collection {
  width: 100%;
  position: absolute;
  top: 0px;
  bottom: 71px;
}
.dxrd-datasource-parameters .dxrd-datasource-parameters-collection .dxrd-datasource-parameters-container {
  border-width: 1px;
  border-style: solid;
}
.dxrd-datasource-parameters .dxrd-datasource-parameters-collection .dxrd-datasource-parameters-container .dxrd-datasource-parameters-empty-text {
  vertical-align: middle;
  line-height: 26px;
}
.dxrd-datasource-parameters .dxrd-datasource-parameters-collection .dxrd-group-header {
  font-weight: normal;
  font-size: 14px;
  font-family: 'Helvetica Neue', 'Segoe UI', Helvetica, Verdana, san-serif;
  text-transform: none;
}
.dxrd-querybuilder-popup {
  cursor: default;
}
.dxrd-querybuilder-popup .dx-popup-content {
  padding: 0;
}
.dxrd-querybuilder-popup .dxrd-querybuilder-popup-content {
  height: 100%;
  padding-bottom: 55px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dxrd-querybuilder-popup .dxrd-querybuilder-popup-buttons {
  bottom: 0;
  position: absolute;
  width: 100%;
}
.dxrd-wizard.dxrd-querybuilder-popup .dxrd-querybuilder-popup-buttons {
  padding: 24px;
  height: auto;
}
.dxrd-querybuilder-popup.dxrd-querybuilder-popup .dxrd-querybuilder-popup-buttons {
  border-top-width: 1px;
  border-top-style: solid;
  height: 55px;
}
.dxrd-querybuilder-popup .dxrd-querybuilder-popup-buttons .dxrd-wizard-btn {
  text-align: center;
  margin-top: 10px;
  min-width: 95px;
}
.dx-ltr .dxrd-querybuilder-popup .dxrd-querybuilder-popup-buttons .dxrd-wizard-btn.left {
  float: left;
  margin-left: 0;
  margin-right: 8px;
}
.dx-rtl .dxrd-querybuilder-popup .dxrd-querybuilder-popup-buttons .dxrd-wizard-btn.left {
  float: right;
  margin-right: 0;
  margin-left: 8px;
}
.dx-ltr .dxrd-querybuilder-popup .dxrd-querybuilder-popup-buttons .dxrd-wizard-btn.right {
  float: right;
  margin-left: 10px;
  margin-right: 0;
}
.dx-rtl .dxrd-querybuilder-popup .dxrd-querybuilder-popup-buttons .dxrd-wizard-btn.right {
  float: left;
  margin-right: 10px;
  margin-left: 0;
}
.dxrd-querybuilder-popup .dxrd-querybuilder-popup-buttons .dxrd-querybuilder-popup-button {
  text-align: center;
  margin-top: 10px;
  min-width: 95px;
  float: right;
  margin: 10px;
  margin-left: 0px;
  min-width: 100px;
}
.dxrd-querybuilder-popup .dxrd-querybuilder-popup-buttons .dxrd-querybuilder-popup-button-left {
  text-align: center;
  margin-top: 10px;
  min-width: 95px;
  float: left;
  margin: 10px;
}
.dxrd-querybuilder-popup .dx-rtl .dxrd-querybuilder-popup-button {
  float: left;
  margin-left: 10px;
  margin-right: 0px;
}
.dxrd-querybuilder-popup .dx-rtl .dxrd-querybuilder-popup-button-left {
  float: right;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup {
  display: flex;
  flex-direction: row;
  height: 95%;
  margin: 18px 9px;
  position: relative;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-data-container {
  position: relative;
  flex: 1;
  display: flex;
  margin: 0 9px;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-data-container .dxrd-federation-data-container-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 300px;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .ui-resizable-handle {
  opacity: 0;
  z-index: 100;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .ui-resizable-handle:hover {
  opacity: 0.8;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-treelist-wrapper {
  width: 250px;
  min-width: 200px;
  margin: 0 9px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border-width: 1px;
  border-style: solid;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-treelist-wrapper .dxrd-federation-treelist .ui-draggable .dx-treelist-caption .dx-treelist-text {
  font-weight: bold;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-treelist-wrapper .ui-resizable-handle {
  right: -15px;
  width: 10px;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .ui-resizable-handle {
  bottom: -15px;
  height: 10px;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dxrd-union-result-source-grid {
  border-width: 1px;
  border-style: solid;
  flex: 3;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dxrd-union-result-source-grid .dxrd-image-recycle-bin {
  cursor: pointer;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dxrd-union-result-source-grid .dxrd-image-recycle-bin svg {
  width: 18px;
  height: 18px;
  margin-left: -1px;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dxrd-union-result-alias-grid {
  border-width: 1px;
  border-style: solid;
  height: 42%;
  margin-top: 18px;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dx-accordion {
  border-width: 1px;
  border-style: solid;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dx-accordion .dx-accordion-header {
  padding: 3px;
  padding-left: 10px;
  font-weight: bold;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dx-accordion .dx-accordion-content {
  height: calc(100% - 45px);
  border-width: 1px;
  border-style: solid;
  margin: 10px;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dx-accordion:not(.dx-accordion-collapsed) .dx-accordion-content {
  min-height: 38px;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dx-accordion:not(.dx-accordion-collapsed) .dx-accordion-header {
  height: 25px;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dxrd-transform-result-grid {
  margin-top: 18px;
  max-height: 45%;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dxrd-transform-source-grid {
  flex: 1;
  height: 50%;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dxrd-transform-source-grid.dxrd-transform-source-grid-expanded {
  height: 100%;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dxrd-querybuilder-federation-popup .dxrd-federation-result-grids .dxrd-select-result-alias-grid {
  height: unset;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dx-popup-bottom.dx-toolbar {
  padding: 0px 9px 26px 18px;
}
.dxrd-querybuilder-federation-popup-wrapper .dx-popup-normal .dx-popup-bottom.dx-toolbar .dx-toolbar-items-container .dx-toolbar-after .dx-toolbar-item {
  padding-right: 8px;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-querybuilder-surface {
  border-width: 1px;
  border-style: solid;
  flex: 1;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-right-panel {
  z-index: 2;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-properties-grid .dxqb-selected-properties {
  padding-top: 20px;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid {
  border-width: 1px;
  border-style: solid;
  position: relative;
  margin: 18px 0 0 0;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dxrd-querybuilder-column-container {
  display: flex;
  width: 100%;
  padding: 4px 0;
  justify-content: center;
  align-items: center;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dxrd-querybuilder-column-container .dxrd-querybuilder-column-data {
  flex: 1;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dxrd-querybuilder-column-container.dxrd-querybuilder-column-container-full .dxrd-querybuilder-column-data > .dx-texteditor-input {
  padding-top: 4px;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dxrd-querybuilder-grid-actions svg {
  width: 20px;
  height: 20px;
  cursor: pointer;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dx-accordion-content {
  height: 250px;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dxrd-querybuilder-column-editorswitch {
  width: 30px;
  height: 30px;
  margin: 0 4px;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dxrd-querybuilder-column-editorswitch .dx-button {
  height: 100%;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dxrd-querybuilder-column-editorswitch .dx-button .dxrd-svg-wizard-expressions {
  margin-left: -3px;
  margin-top: -3px;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dx-datagrid .dx-datagrid-headers .dx-datagrid-content .dx-datagrid-table .dx-row td {
  padding-left: 7px;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dx-datagrid .dx-datagrid-content .dx-data-row td:not(.dx-editor-cell) {
  vertical-align: middle;
  padding-top: 4px;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-select-result-alias-grid .dx-datagrid .dx-datagrid-content .dx-data-row td .dxrd-querybuilder-column-container .dx-texteditor-input-container input {
  font-size: 14px;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-federation-action-add {
  position: absolute;
  margin-top: 4px;
  z-index: 1;
  width: 26px;
  height: 26px;
  right: 12px;
  left: unset;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-popup-normal .dxrd-federation-result-grids .dxrd-federation-action-add .dx-icon-add {
  left: 3px;
  top: 3px;
  position: absolute;
}
.dxrd-querybuilder-federation-popup-wrapper.dxrd-querybuilder-select-federation-popup .dx-rtl .dxrd-federation-result-grids .dxrd-federation-action-add {
  right: unset;
  left: 12px;
}
.dxrd-federation-addqueries-popover.dx-popover-wrapper .dx-popup-content {
  padding: 0;
}
.dxrd-federation-addqueries-popover.dx-popover-wrapper .dx-popup-content .dxrd-federation-addqueries-popover-list {
  padding: 5px;
}
.dxrd-federation-addqueries-popover.dx-popover-wrapper .dx-popup-content .dxrd-federation-addqueries-popover-list .dx-list-item {
  border-top: none;
}
.dxrd-federation-addqueries-popover.dx-popover-wrapper .dx-popup-content .dxrd-federation-addqueries-popover-list .dx-list-item .dxrd-federation-addqueries-popover-list-item .dx-text-content {
  text-align: left;
}
.dxrd-wizard.dx-editors .dxrd-wizard-content .dxrd-collectioneditor-action {
  margin-left: 0;
  margin-right: 10px;
  float: left;
}
.dxrd-wizard.dx-editors .dxrd-wizard-content .dxrd-group-header {
  padding-left: 13px;
}
.dxrd-wizard.dx-editors .dx-rtl .dxrd-wizard-content .dxrd-collectioneditor-action {
  margin-left: 10px;
  margin-right: 0;
  float: right;
}
.dxrd-wizard.dx-editors .dx-rtl .dxrd-wizard-content .dxrd-group-header {
  padding-right: 13px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-create-query-page .dx-radiogroup {
  margin-right: 0;
}
.dxrd-multiqueries-sqldatasource-wizard.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-page .dx-filtereditor-tree {
  padding-right: 20px;
  padding-left: 0px;
}
.dxrd-multiqueries-sqldatasource-wizard.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-page .dx-treelist-item .dx-treelist-text-with-checkbox {
  padding-right: 31px;
  padding-left: 0px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-treelist-item .dx-treelist-collapsedbutton {
  margin-right: 10px;
  margin-left: 9px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-treelist-item .dx-treelist-action.dx-button {
  margin-right: 6px;
  margin-left: 0px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-accordion-content.dx-fieldset {
  padding: 31px 159px 26px 139px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-editors .dx-editor .dx-field-label.dx-accordion-header {
  left: 75%;
  right: auto;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-navigation .dxrd-wizard-btn.left {
  float: right;
  margin-right: 0;
  margin-left: 8px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-navigation .dxrd-wizard-btn.right {
  float: left;
  margin-left: 0;
  margin-right: 8px;
}
.dxrd-wizard .dx-rtl .dxrd-datasource-parameters .dxrd-datasource-parameters-header .dxrd-datasource-parameters-header-text {
  float: right;
}
.dx-add-queries-page-checkbox {
  position: absolute;
}

/**
* DevExpress HTML/JS Reporting (dist\css\dx-reportdesigner.css)
* Version:  24.2.8
* Build date: Jun 17, 2025
* Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
* License: https://www.devexpress.com/Support/EULAs/universal.xml
*/
.dxrd-error-page {
  font-family: "Helvetica Neue", "Segoe UI", helvetica, verdana, sans-serif;
  background: #FFF0F0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  row-gap: 12px;
  height: 100%;
}
.dxrd-error-page-icon {
  width: 64px;
  height: 64px;
}
.dxrd-error-page-title {
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  color: #660000;
}
.dxrd-error-page-content {
  font-size: 16px;
  text-align: center;
  color: rgba(32, 32, 32, 0.87);
}
.dx-ltr.dxrd-designer-wrapper.dxcd-designer .dxrd-toolbar-wrapper {
  left: 326px;
}
.dx-rtl.dxrd-designer-wrapper.dxcd-designer .dxrd-toolbar-wrapper {
  right: 326px;
}
.dxrd-designer-wrapper.dxcd-designer .dxrd-surface-wrapper {
  top: 0;
  height: 100%;
}
.dxrd-designer-wrapper.dxcd-designer .dxrd-surface-wrapper .dxrd-surface {
  height: 100%;
  display: inline-block;
}
.dxrd-designer-wrapper.dxcd-designer .dxrd-surface-wrapper .dxrd-surface .dxrd-viewport {
  overflow: auto;
  position: absolute;
  width: inherit;
  height: inherit;
  top: 50%;
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background-color: #FFFFFF;
  box-shadow: rgba(0, 0, 0, 0.15) 0 2px 8px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  padding: 30px;
}
.dxrd-designer-wrapper.dxcd-designer .dxrd-right-panel {
  top: 1px;
}
.dx-ltr.dxrd-designer-wrapper.dxcd-designer .dxrd-right-panel {
  right: 1px;
}
.dx-rtl.dxrd-designer-wrapper.dxcd-designer .dxrd-right-panel {
  left: 1px;
}
.dxrd-designer-wrapper.dxcd-designer .dx-chart-left-panel {
  border-right-width: 1px;
  border-right-style: solid;
  width: 326px;
  height: 100%;
  position: absolute;
  top: 1px;
}
.dx-ltr.dxrd-designer-wrapper.dxcd-designer .dx-chart-left-panel {
  left: 1px;
}
.dx-rtl.dxrd-designer-wrapper.dxcd-designer .dx-chart-left-panel {
  right: 1px;
}
.dxrd-designer-wrapper.dxcd-designer .dx-chart-left-panel .dxcd-indicators-list {
  letter-spacing: normal;
  height: 300px;
}
.dxrd-designer-wrapper.dxcd-designer .dx-chart-left-panel .dxcd-indicators-list .dx-list-search {
  border-top: none;
  border-right: none;
  border-left: none;
  height: 36px;
  font-size: 14px;
  border-radius: 0;
  margin-bottom: 0;
}
.dxrd-designer-wrapper.dxcd-designer .dx-chart-left-panel .dxcd-indicators-list .dx-list-search .dx-texteditor-input {
  font-size: 14px;
}
.dxrd-designer-wrapper.dxcd-designer .dx-chart-left-panel .dxcd-indicators-list .dx-list-search .dx-placeholder {
  font-size: 14px;
}
.dxrd-designer-wrapper.dxcd-designer .dx-chart-left-panel .dxrd-right-panel {
  left: 0;
  top: 0;
  position: absolute;
  height: 100%;
  border-right-width: 1px;
  border-right-style: solid;
  border-left: none;
}
.dxrd-designer-wrapper.dxcd-designer .dx-chart-left-panel .dxrd-right-panel .dxcd-chart-structure {
  height: 100%;
}
.dxrd-designer-wrapper.dxcd-designer .dx-chart-left-panel .dxrd-right-panel .dxcd-chart-structure .dx-treelist-item .dx-treelist-image-empty {
  display: none;
}
.dxrd-designer-wrapper.dxcd-designer .propertygrid-editor-checkbox {
  left: 0;
}
.dxcd-summary-function-content .dxcd-summaryfunction-arg:hover .dxcd-summaryFunction-remove {
  pointer-events: auto;
}
.dxcd-summary-function-content .dxcd-summaryfunction-arg:hover .dxcd-summaryFunction-remove .dxcd-summaryFunction-remove-icon {
  display: block;
}
.dxcd-summary-function-content .dxcd-summaryfunction-arg:hover .dx-texteditor {
  margin-right: 30px;
}
.dxcd-summary-function-content .dxcd-summaryfunction-arg .dxcd-summaryFunction-remove {
  pointer-events: none;
  cursor: pointer;
  float: right;
}
.dxcd-summary-function-content .dxcd-summaryfunction-arg .dxcd-summaryFunction-remove .dxcd-summaryFunction-remove-icon {
  display: none;
  background-size: 24px;
  background-position: center;
  position: absolute;
  width: 26px;
  height: 26px;
  right: 0;
}
.dxcd-summary-function-content .dxcd-summaryfunction-arg .dx-texteditor {
  -moz-transition: all ease-in-out 0.1s;
  -o-transition: all ease-in-out 0.1s;
  -webkit-transition: all ease-in-out 0.1s;
  transition: all ease-in-out 0.1s;
}
.dxcd-summary-function-content .dxrcd-summaryfunction-addnew {
  cursor: pointer;
  border-bottom: 1px dashed;
}

.dxrd-error-page {
  font-family: "Helvetica Neue", "Segoe UI", helvetica, verdana, sans-serif;
  background: #FFF0F0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  row-gap: 12px;
  height: 100%;
}
.dxrd-error-page-icon {
  width: 64px;
  height: 64px;
}
.dxrd-error-page-title {
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  color: #660000;
}
.dxrd-error-page-content {
  font-size: 16px;
  text-align: center;
  color: rgba(32, 32, 32, 0.87);
}
.dx-designer {
  /*override Bootstap styles*/
  /*revert Devextreme styles*/
  /*
        ***not used border-box***
        .dx-quill-container
        .dx-htmleditor-content
        .dx-filemanager-progresspanel
        .dx-filemanager .dx-filemanager-container 
    */
  /*
        ***not used content-box***
        .dx-datagrid-drag-header 
        .dx-pivotgrid td
        .dx-treelist-drag-header
        .dx-calendar-body td 
        .dx-resize-frame
        .dx-scrollview-pull-down
    */
}
.dx-designer *,
.dx-designer *:before,
.dx-designer *:after {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}
.dx-designer svg {
  vertical-align: baseline;
}
.dx-designer .dx-widget,
.dx-designer .dx-widget *,
.dx-designer .dx-widget :after,
.dx-designer .dx-widget :before,
.dx-designer .dx-widget:after,
.dx-designer .dx-widget:before,
.dx-designer .dx-fieldset,
.dx-designer .dx-fieldset *,
.dx-designer .dx-scrollable-content,
.dx-designer .dx-sortable-placeholder,
.dx-designer .dx-overlay-wrapper,
.dx-designer .dx-overlay-wrapper *,
.dx-designer .dx-overlay-wrapper :after,
.dx-designer .dx-overlay-wrapper :before,
.dx-designer .dx-overlay-wrapper:after,
.dx-designer .dx-overlay-wrapper:before {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.dx-designer .dx-radiogroup .dx-widget .dx-radiobutton-icon:before {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}
.dx-designer .dx-searchbox .dx-icon-search:before,
.dx-designer .dx-toolbar .dx-button .dx-icon,
.dx-designer .dx-toolbar-item,
.dx-designer .dx-toolbar-menu-container,
.dx-designer .dx-overlay-content .dx-popup-content {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}
.ui-selectable-helper.dxrd-selection-content {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}
.dxrd-image-surface-picturebox_placeholder {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI0LjMuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCAxNiAxNiIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMTYgMTY7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5CbGFja3tmaWxsOiMzRDNEM0Q7fQo8L3N0eWxlPgo8cGF0aCBjbGFzcz0iQmxhY2siIGQ9Ik0xNCwxSDJDMS41LDEsMSwxLjUsMSwydjEyYzAsMC41LDAuNSwxLDEsMWgxMmMwLjUsMCwxLTAuNSwxLTFWMkMxNSwxLjUsMTQuNSwxLDE0LDF6IE0xMywxM0gzVjNoMTBWMTN6CgkgTTEwLDRjMS4xLDAsMiwwLjksMiwycy0wLjksMi0yLDJTOCw3LjEsOCw2UzguOSw0LDEwLDR6IE00LDEyVjlsMi0ybDUsNUg0eiIvPgo8L3N2Zz4K);
}
.dxrd-image-surface-bounded:after {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI0LjMuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCAxNiAxNiIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMTYgMTY7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojM0QzRDNEO30KPC9zdHlsZT4KPHBhdGggY2xhc3M9InN0MCIgZD0iTTgsMkM1LjIsMiwzLDMuMywzLDV2NmMwLDEuNywyLjIsMyw1LDNzNS0xLjMsNS0zVjVDMTMsMy4zLDEwLjgsMiw4LDJ6IE04LDZDNi4yLDYsNS4xLDUuMiw1LDUKCWMwLjEtMC4yLDEuMi0xLDMtMXMyLjksMC44LDMsMUMxMC45LDUuMiw5LjgsNiw4LDZ6Ii8+Cjwvc3ZnPgo=);
}
.dxrd-image-surface-bounded-notvalid:after {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI0LjMuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCAxNiAxNiIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMTYgMTY7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojRkYwMDAwO30KPC9zdHlsZT4KPHBhdGggY2xhc3M9InN0MCIgZD0iTTgsMkM1LjIsMiwzLDMuMywzLDV2NmMwLDEuNywyLjIsMyw1LDNzNS0xLjMsNS0zVjVDMTMsMy4zLDEwLjgsMiw4LDJ6IE04LDZDNi4yLDYsNS4xLDUuMiw1LDUKCWMwLjEtMC4yLDEuMi0xLDMtMXMyLjksMC44LDMsMUMxMC45LDUuMiw5LjgsNiw4LDZ6Ii8+Cjwvc3ZnPgo=);
}
.dxrd-image-surface-bounded-warning:after {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI0LjMuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCAxNiAxNiIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMTYgMTY7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDpyZ2JhKDI0MCwgMTczLCA3OCwgMSk7fQo8L3N0eWxlPgo8cGF0aCBjbGFzcz0ic3QwIiBkPSJNOCwyQzUuMiwyLDMsMy4zLDMsNXY2YzAsMS43LDIuMiwzLDUsM3M1LTEuMyw1LTNWNUMxMywzLjMsMTAuOCwyLDgsMnogTTgsNkM2LjIsNiw1LjEsNS4yLDUsNQoJYzAuMS0wLjIsMS4yLTEsMy0xczIuOSwwLjgsMywxQzEwLjksNS4yLDkuOCw2LDgsNnoiLz4KPC9zdmc+Cgo=);
}
.dxrd-image-ghost-bounded {
  background-repeat: no-repeat;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI0LjMuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCAxNiAxNiIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgMTYgMTY7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojM0QzRDNEO30KPC9zdHlsZT4KPHBhdGggY2xhc3M9InN0MCIgZD0iTTgsMkM1LjIsMiwzLDMuMywzLDV2NmMwLDEuNywyLjIsMyw1LDNzNS0xLjMsNS0zVjVDMTMsMy4zLDEwLjgsMiw4LDJ6IE04LDZDNi4yLDYsNS4xLDUuMiw1LDUKCWMwLjEtMC4yLDEuMi0xLDMtMXMyLjksMC44LDMsMUMxMC45LDUuMiw5LjgsNiw4LDZ6Ii8+Cjwvc3ZnPgo=);
}
.dxrd-pivot-content {
  direction: ltr;
}
.dxrd-surface-rtl .dxrd-pivot-content {
  direction: rtl;
}
.dxrd-pivot-content .dxrd-pivot-filter-area {
  background: #dddddd;
  border-bottom: 1px solid #bbbbbb;
  height: 28px;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-filter-area {
  background: transparent;
  border-color: transparent;
}
.dxrd-pivot-content .dxrd-pivot-fields-wrapper {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  height: 20px;
  width: 100%;
  padding: 4px;
  margin-right: -1px;
  text-align: left;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-fields-wrapper {
  margin-left: -1px;
  margin-right: 0px;
  text-align: right;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-fields-wrapper {
  text-overflow: inherit;
}
.dxrd-pivot-content .dxrd-pivot-fields-wrapper .dxrd-pivot-field {
  text-align: left;
}
.dxrd-pivot-content-rtl.dxrd-pivot-content .dxrd-pivot-fields-wrapper .dxrd-pivot-field {
  text-align: right;
}
.dxrd-pivot-content .dxrd-pivot-field {
  overflow: hidden;
}
.dxrd-pivot-content .dxrd-pivot-field .dxrd-pivot-field-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  height: 100%;
  padding: 1px 4px 0px 4px;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-field .dxrd-pivot-field-content {
  text-overflow: inherit;
}
.dxrd-pivot-content .dxrd-pivot-field-wrapper {
  border: 1px solid #bbbbbb;
  background: #eeeeee;
  display: inline-block;
  height: 18px;
  margin-right: 1px;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-field-wrapper {
  background: transparent;
  border-color: transparent;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-field-wrapper {
  margin-left: 1px;
  margin-right: 0px;
}
.dxrd-pivot-content .dxrd-pivot-field-selected {
  background: #A9A9A9;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-field-selected {
  background: transparent;
}
.dxrd-pivot-content .dxrd-pivot-total-wrapper {
  min-width: 100px;
  border-top: 1px solid #bbbbbb;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-total-wrapper {
  border-color: transparent;
}
.dxrd-pivot-content .dxrd-pivot-total {
  border-bottom: 1px solid #bbbbbb;
  background: #eeeeee;
  padding: 1px 4px 0px 4px;
  min-height: 19px;
  border-right: 1px solid #bbbbbb;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-total {
  border-left: 1px solid #bbbbbb;
  border-right: 0;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-total {
  background: transparent;
  border-color: transparent;
  color: transparent;
}
.dxrd-pivot-content-rtl.dxrd-pivot-content .dxrd-pivot-total {
  text-align: right;
}
.dxrd-pivot-content:not(.dxrd-pivot-content-rtl).dxrd-pivot-content .dxrd-pivot-total {
  text-align: left;
}
.dxrd-pivot-content .dxrd-pivot-grid-area-left {
  float: left;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-grid-area-left {
  float: right;
}
.dxrd-pivot-content .dxrd-pivot-rows-area {
  position: relative;
  overflow: hidden;
}
.dxrd-pivot-content .dxrd-pivot-rows-header {
  display: table-cell;
  vertical-align: bottom;
  position: relative;
  padding-top: 22px;
  min-width: 100px;
  border-bottom: 1px solid #bbbbbb;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-rows-header {
  border-color: transparent;
}
.dxrd-pivot-content .dxrd-pivot-grid-area-right {
  position: relative;
  overflow: hidden;
}
.dxrd-pivot-content .dxrd-pivot-columns-header {
  height: 28px;
  position: relative;
}
.dxrd-pivot-content .dxrd-pivot-data-area {
  overflow: hidden;
  white-space: nowrap;
}
.dxrd-pivot-content-rtl.dxrd-pivot-content .dxrd-pivot-data-area {
  text-align: right;
}
.dxrd-pivot-content:not(.dxrd-pivot-content-rtl).dxrd-pivot-content .dxrd-pivot-data-area {
  text-align: left;
}
.dxrd-pivot-content .dxrd-pivot-data-field {
  float: left;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-data-field {
  float: right;
}
.dxrd-pivot-content .dxrd-pivot-data-header {
  border-left: 1px solid #bbbbbb;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-data-header {
  border-color: transparent;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-data-header {
  border-right: 1px solid #bbbbbb;
  border-left: 0;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-data-header {
  border-color: transparent;
}
.dxrd-pivot-content .dxrd-pivot-field-empty {
  background: white;
  border-bottom: 1px solid #bbbbbb;
  height: 20px;
  line-height: 19px;
  border-right: 1px solid #bbbbbb;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-field-empty {
  border-left: 1px solid #bbbbbb;
  border-right: 0;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-field-empty {
  border-color: transparent;
  background: transparent;
}
.dxrd-pivot-content .dxrd-pivot-text {
  color: #808080;
  display: inline-block;
  font-size: 11px;
  line-height: normal;
  vertical-align: middle;
  white-space: nowrap;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-text {
  color: transparent;
}
.dxrd-pivot-content .dxrd-pivot-text-wrapper {
  line-height: 27px;
  padding-left: 4px;
  text-align: left;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-text-wrapper {
  text-align: right;
  padding-right: 4px;
}
.dxrd-pivot-content .dxrd-pivot-data-field > .dxrd-pivot-field-wrapper {
  border: none;
  display: block;
  height: 20px;
  border-right: 1px solid #bbbbbb;
  margin-right: 0px;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-data-field > .dxrd-pivot-field-wrapper {
  border-left: 1px solid #bbbbbb;
  margin-left: 0px;
  border-right: 0;
  margin-right: 0px;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-data-field > .dxrd-pivot-field-wrapper {
  border-color: transparent;
}
.dxrd-pivot-content .dxrd-pivot-data-field > .dxrd-pivot-field-wrapper > .dxrd-pivot-field {
  border-bottom: 1px solid #bbbbbb;
  height: 18px;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-data-field > .dxrd-pivot-field-wrapper > .dxrd-pivot-field {
  border-color: transparent;
}
.dxrd-pivot-content .dxrd-pivot-filter-area > .dxrd-pivot-fields-wrapper > .dxrd-pivot-field-wrapper {
  min-width: 100px;
}
.dxrd-pivot-content .dxrd-pivot-columns-header > .dxrd-pivot-fields-wrapper > .dxrd-pivot-field-wrapper {
  min-width: 100px;
}
.dxrd-pivot-content .dxrd-pivot-columns-header > .dxrd-pivot-fields-wrapper {
  padding-left: 0px;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-columns-header > .dxrd-pivot-fields-wrapper {
  padding-right: 0px;
}
.dxrd-pivot-content .dxrd-pivot-rows-area > .dxrd-pivot-total {
  border-right: none;
}
.dxrd-surface-rtl .dxrd-pivot-content .dxrd-pivot-rows-area > .dxrd-pivot-total {
  border-left: none;
}
.dxrd-selection .dxrd-pivot-content .dxrd-pivot-rows-area > .dxrd-pivot-total {
  border-color: transparent;
}
.dxrd-reportdialog .dx-popup-content {
  margin-bottom: 0;
}
.dx-designer .dx-designer-viewport .dxrd-reportdialog .dx-popup-content {
  padding: 20px;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-easy-content {
  margin: 40px 60px 35px 60px;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-converter-content {
  margin: 16px 60px 64px 60px;
  max-width: 100%;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-converter-content .dxrd-image-exlamation-icons {
  padding: 30px 24px;
  display: inline-block;
  vertical-align: top;
  line-height: normal;
  position: relative;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-converter-content .dxrd-image-exlamation-icons svg {
  width: 31px;
  height: 31px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-converter-content .dxrd-reportdialog-content-text {
  display: inline-block;
  vertical-align: middle;
  letter-spacing: 0;
  line-height: normal;
  cursor: default;
  width: 462px;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-converter-content .dxrd-reportdialog-content-text .dxrd-reportdialog-toggle-link {
  display: inline-block;
  cursor: pointer;
  text-decoration: underline;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-converter-content .dxrd-reportdialog-content-text .dxrd-reportdialog-toggle-link:hover {
  text-decoration: none;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-converter-content .dxrd-reportdialog-content-text .dxrd-reportdialog-content-confirm-message {
  display: table-cell;
  height: 66px;
  vertical-align: middle;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-converter-content .dxrd-reportdialog-content-text .dxrd-reportdialog-content-info-message {
  display: inline-block;
  height: 66px;
  vertical-align: middle;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-converter-content .dxrd-reportdialog-content-text .dxrd-reportdialog-scroll-view-content {
  height: 193px;
  margin-top: 10px;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-converter-content .dxrd-reportdialog-content-text .dxrd-reportdialog-scroll-view-content .dxrd-reportdialog-scroll-view-content-text {
  word-wrap: break-word;
  white-space: pre-wrap;
  padding-right: 15px;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-content {
  margin: 10px 10px 5px 10px;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-content .dx-texteditor-container .dx-texteditor-input,
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-content .dx-texteditor-container .dx-placeholder {
  font-size: 14px;
  margin-top: 0;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-content .dxrd-reportdialog-urls .dx-list-item {
  border: none;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-content .dx-fieldset {
  margin: 0 0 6px 0;
  padding: 0;
}
.dxrd-reportdialog .dx-popup-content .dxrd-reportdialog-content .dx-fieldset .dx-field {
  padding: 0;
  background-color: transparent;
}
.dxrd-reportdialog .dx-popup-bottom {
  padding: 0 30px 30px;
}
.dxrd-reportdialog .dx-popup-bottom .dx-toolbar-item {
  padding-right: 6px;
}
.dx-designer .dxrd-parameters-dialog.dxrd-parameters-edit-dialog .dxrd-parameters-popup-content {
  padding: 10px 24px 25px 40px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content {
  width: 100%;
  height: 100%;
  padding: 4px 24px 25px 24px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dx-collectioneditor {
  height: 100%;
  border-color: inherit;
  position: relative;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dx-collectioneditor > div {
  border-color: inherit;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dx-collectioneditor .dx-collectioneditor-header-text {
  opacity: 0.75;
  float: left;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dx-collectioneditor .dx-collectioneditor-header-text .dx-collapsing-image {
  display: none;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dx-collectioneditor .dx-accordion-content {
  position: absolute;
  top: 36px;
  border-color: inherit;
  border-width: 1px;
  border-style: solid;
  bottom: 0px;
  width: 100%;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dx-collectioneditor .dx-accordion-content .dx-scrollable-scroll {
  padding: 0;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dx-collectioneditor .dx-accordion-content .dx-collectioneditor-items {
  height: 100%;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dx-collectioneditor .dx-accordion-content .dx-collectioneditor-items .dxrd-parameter-editing-collection-item {
  padding: 5px 10px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dx-group-header-font {
  padding-left: 0px;
  opacity: 0.5;
  text-transform: none;
  font-size: 12px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-rtl .dx-field .dx-field-value .dxrd-editormenu-box {
  margin-right: 4px;
  margin-left: 0px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-field {
  padding-top: 6px;
  padding-bottom: 6px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-field .dx-field-label {
  font-size: 12px;
  opacity: 0.75;
  right: 265px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-field .dx-field-value {
  height: auto;
  width: 250px;
  display: flex;
  flex-direction: row-reverse;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-field .dx-field-value .dx-texteditor {
  height: 30px;
  border-radius: 0px;
  width: inherit;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-field .dx-field-value .dx-texteditor .dx-texteditor-input {
  font-size: 12px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-field .dx-field-value .dx-datebox-container {
  width: inherit;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-field .dx-field-value .dx-popup-general {
  width: inherit;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-field .dx-field-value .dxrd-editormenu-box {
  margin-left: 4px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-field .dx-field-value .dxrd-editormenu-box .dx-button-content {
  width: 28px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dx-field .dx-field-value .dxrd-editormenu-box .dx-button-content .dxrd-svg-wizard-expressions svg {
  width: max-content;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameters-checkbox {
  min-height: 24px;
  padding-top: 3px;
  padding-bottom: 3px;
  height: 24px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameters-checkbox .dx-field-value {
  height: 24px;
  min-height: 24px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameters-checkbox .dx-field-value .propertygrid-editor-checkbox {
  margin: 0;
  float: none;
  width: 100%;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameters-checkbox .dx-field-value .propertygrid-editor-checkbox .dx-checkbox-icon {
  font-size: 12px;
  width: 16px;
  height: 16px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameters-checkbox .dx-field-value .propertygrid-editor-checkbox .dx-checkbox-text {
  font-size: 12px;
  opacity: 0.75;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameter-edit-content-grid {
  width: calc(50% - 16px);
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameter-edit-content-grid.dxrd-parameters-properties {
  float: left;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameter-edit-content-grid.dxrd-parameters-settings {
  float: right;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameter-group-editor.dxrd-parameters-group-offset {
  padding-top: 22px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameter-group-editor .dx-accordion-header {
  right: 0px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameter-group-editor .dx-accordion-header.dxd-text-primary {
  width: 28%;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameter-group-editor .dx-field {
  padding-top: 3px;
  padding-bottom: 3px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameter-group-editor .dx-accordion-content {
  border: none;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-lookup-values-collectioneditor {
  height: 305px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-popup-content .dxrd-parameter-edit-content .dxrd-parameter-values-collectioneditor {
  height: 148px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content {
  width: 100%;
  height: 100%;
  min-width: 714px;
  display: flex;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dx-collectioneditor-actions-wrapper {
  display: flex;
  width: 300px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dx-collectioneditor-actions-wrapper .dx-collectioneditor-action {
  margin-right: 4px;
  margin-bottom: 2px;
  margin-left: 0;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dx-collectioneditor-actions-wrapper .dx-collectioneditor-action .dx-button-content {
  padding: 0;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dx-collectioneditor-actions-wrapper .dx-collectioneditor-action.dx-collectioneditor-action-right {
  margin-right: 0;
  margin-left: 4px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dx-collectioneditor-actions-wrapper .dx-collectioneditor-action-separator {
  margin: auto;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dxrd-parameters-content-list {
  border-width: 1px;
  border-style: solid;
  width: 300px;
  margin-right: 46px;
  height: 100%;
  float: left;
  position: relative;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dxrd-parameters-content-list .dx-collectioneditor-empty {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  margin-top: -37px;
  text-align: center;
  overflow: hidden;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dx-icon-dxrd {
  width: 16px;
  height: 16px;
  position: relative;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dx-icon-dxrd svg {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dx-icon-dxrd.dxrd-image-recycle-bin svg {
  width: 12px;
  height: 12px;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameter-edit-content {
  width: 810px;
  float: right;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameter-edit-content.dx-empty {
  position: relative;
}
.dx-designer .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-empty {
  width: 100%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  overflow: hidden;
}
.dx-designer .dx-popup-bottom.dx-toolbar {
  padding: 0px 30px 26px 30px;
}
.dx-designer .dx-popup-bottom.dx-toolbar .dx-toolbar-item {
  padding: 0 6px 0 0;
}
.dx-designer .dx-popup-bottom.dx-toolbar .dx-toolbar-item .dx-button {
  height: 30px;
  font-size: 12px;
}
.dx-designer .dx-popup-bottom.dx-toolbar .dx-toolbar-item .dx-button .dx-button-content {
  padding-top: 6px;
}
.dx-designer-viewport:not(.dx-designer-viewport-xl) .dxrd-parameters-dialog .dxrd-parameters-popup-content {
  padding-right: 0px;
}
.dx-designer-viewport:not(.dx-designer-viewport-xl) .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list-wrapper .dx-collectioneditor-actions-wrapper {
  width: 280px;
}
.dx-designer-viewport:not(.dx-designer-viewport-xl) .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameters-content-list {
  width: 280px;
}
.dx-designer-viewport:not(.dx-designer-viewport-xl) .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameter-edit-content {
  width: 410px;
}
.dx-designer-viewport:not(.dx-designer-viewport-xl) .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameter-edit-content-grid {
  width: auto;
  padding-right: 24px;
}
.dx-designer-viewport:not(.dx-designer-viewport-xl) .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameter-edit-content-grid.dxrd-parameters-properties {
  float: none;
}
.dx-designer-viewport:not(.dx-designer-viewport-xl) .dxrd-parameters-dialog .dxrd-parameters-content .dxrd-parameter-edit-content-grid.dxrd-parameters-settings {
  float: none;
}
.dxrd-scripts {
  width: 100%;
  height: 100%;
}
.dxrd-scripts .dxrd-designer-wrapper .dxrd-toolbar-wrapper {
  left: 0;
  right: 0;
}
.dxrd-scripts .dxrd-designer-wrapper .dxrd-toolbar-wrapper .dxrd-toolbar-selectbox {
  vertical-align: top;
  padding-top: 18px;
}
.dxrd-scripts .dxrd-designer-wrapper .dxrd-scripts-wrapper {
  position: absolute;
  top: 64px;
  left: 0;
  right: 0;
  bottom: 0;
}
.dxrd-scripts .dxrd-designer-wrapper .dxrd-scripts-wrapper .dxrd-scripts-editor {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: left;
}
.dxrd-summaryeditor-popup .propertygrid-editor-checkbox {
  width: 100%;
}
.dxrd-summaryeditor-popup .dx-editors .dx-fieldset .dx-field {
  padding-right: 30px;
}
.dxrd-summaryeditor-popup .dx-fieldset {
  padding-top: 24px;
}
.dxrd-summaryeditor-popup .dx-editors .dx-editor .dx-field-label.dx-accordion-header {
  left: 30px;
}
.dxrd-summaryeditor-popup .dx-checkbox-text {
  padding-left: 33px;
}
.dxrd-summaryeditor-popup .dx-field-value:not(.dx-widget) > .dx-checkbox {
  margin: 0;
}
.dxrd-summaryeditor-popup .dxrd-first-checkbox {
  padding-top: 3px;
}
.dx-editors .dx-checkbox-text {
  font-size: 11px;
}
.dx-designer .dxrd-designer-wrapper .dxrd-surface-wrapper:empty {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-image: linear-gradient(90deg, rgba(245, 245, 245, 0) 0%, rgba(245, 245, 245, 0.5) 50%, rgba(245, 245, 245, 0) 100%), /* properties group start*/ radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), radial-gradient(141px 25px at center, #f5f5f5 100%, transparent 0), radial-gradient(91px 20px at center, #f5f5f5 100%, transparent 0), /* properties group end */ /* property grid */ linear-gradient(white 100%, transparent 0), /* designer surface rows start */ radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 37px at center, #f5f5f5 100%, transparent 0), radial-gradient(50% 27px at center, #f5f5f5 100%, transparent 0), /* designer surface rows end */ /* designer surface */ linear-gradient(white 100%, transparent 0), /* tabs */ radial-gradient(#7a7a7a 64px, transparent 0), radial-gradient(#7a7a7a 64px, transparent 0), radial-gradient(#7a7a7a 64px, transparent 0), radial-gradient(#7a7a7a 64px, transparent 0), /* tab panel */ linear-gradient(#606060 100%, transparent 0);
  background-size: 250px 100%, /* properties group start*/ 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, 282px 12px, 182px 12px, /* properties group end */ /* property grid */ 375px 100%, /* designer surface rows start */ 40% 12px, 40% 12px, 40% 12px, 25% 12px, 40% 12px, 40% 12px, 40% 12px, 25% 12px, 40% 12px, 40% 12px, 40% 12px, 25% 12px, 40% 12px, 40% 12px, 40% 12px, 25% 12px, 40% 12px, 40% 12px, 40% 12px, 25% 12px, 40% 12px, 40% 12px, 40% 12px, 25% 12px, /* designer surface rows end */ /* designer surface*/ 50% 100%, /* tabs */ 32px 32px, 32px 32px, 32px 32px, 32px 32px, /* tab panel */ 48px 100%;
  background-position: -50% 0, /* properties group start*/ right 78px top 114px, right 178px top 134px, right 78px top 176px, right 178px top 196px, right 78px top 238px, right 178px top 258px, right 78px top 300px, right 178px top 320px, right 78px top 362px, right 178px top 382px, right 78px top 424px, right 178px top 444px, right 78px top 486px, right 178px top 506px, right 78px top 548px, right 178px top 568px, /* properties group end */ /* property grid */ right 48px top 64px, /* designer surface rows start */ 35% 188px, 35% 208px, 35% 228px, 28% 248px, 35% 290px, 35% 310px, 35% 330px, 28% 350px, 35% 392px, 35% 412px, 35% 432px, 28% 452px, 35% 494px, 35% 514px, 35% 534px, 28% 554px, 35% 596px, 35% 616px, 35% 636px, 28% 656px, 35% 698px, 35% 718px, 35% 738px, 28% 758px, /* designer surface rows end */ /* designer surface*/ 32% 108px, /* tabs */ right 8px top 92px, right 8px top 132px, right 8px top 172px, right 8px top 212px, /* tab panel */ right top 64px;
  animation: reportloading 1.5s infinite;
}
@keyframes reportloading {
  to {
    background-position: 150% 0, /* properties group start*/ right 78px top 114px, right 178px top 134px, right 78px top 176px, right 178px top 196px, right 78px top 238px, right 178px top 258px, right 78px top 300px, right 178px top 320px, right 78px top 362px, right 178px top 382px, right 78px top 424px, right 178px top 444px, right 78px top 486px, right 178px top 506px, right 78px top 548px, right 178px top 568px, /* properties group end */ /* property grid */ right 48px top 64px, /* designer surface rows start */ 35% 188px, 35% 208px, 35% 228px, 28% 248px, 35% 290px, 35% 310px, 35% 330px, 28% 350px, 35% 392px, 35% 412px, 35% 432px, 28% 452px, 35% 494px, 35% 514px, 35% 534px, 28% 554px, 35% 596px, 35% 616px, 35% 636px, 28% 656px, 35% 698px, 35% 718px, 35% 738px, 28% 758px, /* designer surface rows end */ /* designer surface*/ 32% 108px, /* tabs */ right 8px top 92px, right 8px top 132px, right 8px top 172px, right 8px top 212px, /* tab panel */ right top 64px;
  }
}
.minor-graduation {
  stroke: #eeeeee;
  stroke-width: 1px;
}
.major-graduation {
  stroke: #dbdbdb;
  stroke-width: 1px;
}
.ruler {
  fill: #fee7ab;
}
.ruler-mark {
  fill: gray;
  font-size: xx-small;
  cursor: default;
}
.ruler-graduation {
  stroke: gray;
}
.dx-designer .dxrd-page-color-scheme-colorpicker .dx-popup-content {
  padding: 20px;
}
.dx-designer .dxrd-page-color-scheme-colorpicker .dxrd-page-color-scheme-colorpicker-editor-container {
  padding-bottom: 10px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.dx-designer .dxrd-page-color-scheme-colorpicker .dxrd-page-color-scheme-colorpicker-buttons {
  margin-top: 20px;
  text-align: right;
}
.dx-designer .dxrd-page-color-scheme-colorpicker .dxrd-page-color-scheme-colorpicker-buttons .dxrd-page-color-scheme-colorpicker-button {
  width: 100px;
  float: right;
  margin-left: 8px;
}
.dxrd-wizard.dxrd-master-detail-report-wizard .dxrd-wizard-navigation .dxrd-wizard-btn {
  min-width: 100px;
}
.dxrd-wizard.dxrd-master-detail-report-wizard .dxrd-wizard-navigation .dxrd-wizard-btn.right {
  margin-left: 8px;
}
.dxrd-wizard.dxrd-master-detail-report-wizard .dxrd-wizard-navigation .dxrd-wizard-btn.left {
  margin-right: 8px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 20px 21px 20px 21px;
  padding: 0;
  height: auto;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-wizard-connections {
  height: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-wizard-connections .dxrd-wizard-list {
  height: 100%;
}
.dxrd-multiqueries-sqldatasource-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page {
  margin: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-add-queries-page {
  position: absolute;
  top: 40px;
  height: auto;
  bottom: 0px;
  left: 0;
  right: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-datasourceoperation {
  position: absolute;
  height: 65px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-availabledatasources {
  position: absolute;
  top: 65px;
  width: 100%;
  bottom: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-left-panel,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-right-panel {
  width: 45%;
  position: absolute;
  height: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-left-panel .dxrd-wizard-panel-content,
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-right-panel .dxrd-wizard-panel-content {
  position: absolute;
  top: 42px;
  bottom: 0px;
  width: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-left-panel {
  left: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-right-panel {
  right: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-splitter {
  width: 64px;
  height: 223px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin-top: 40px;
  margin-left: auto;
  margin-right: auto;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dxrd-wizard-splitter-action {
  width: 24px;
  height: 24px;
  display: block;
  padding: 0;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 5px;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page {
  width: 360px;
  height: 209px;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list {
  padding-top: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page {
  width: 360px;
  margin: 0 auto;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list {
  padding: 50px 0 25px 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list > div {
  width: 240px;
  display: inline-block;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list .dxrd-wizard-selectlabelproduct-list-label {
  width: 115px;
  position: relative;
  top: -6px;
  padding-bottom: 5px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list .dx-texteditor-input {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectproduct-props > div {
  padding: 5px 0 5px 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectproduct-props > div > span:first-child {
  width: 115px;
  display: inline-block;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props {
  height: 50px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize {
  width: 305px;
  float: left;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize > span {
  top: -5px;
  position: relative;
  width: 130px;
  display: inline-block;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize > div {
  display: inline-block;
  width: 150px;
  height: 24px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize > div .dx-texteditor-input {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-pagesizetext {
  font-size: 7pt;
  clear: both;
  opacity: 0.5;
  margin-left: 135px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-radiounit {
  width: 329px;
  float: right;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-radiounit .dx-radio-value-container:first-child {
  padding-left: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props {
  width: 305px;
  line-height: 22px;
  float: left;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props .dxrd-wizard-customizelabel-prop-whis-top-margin {
  margin-top: 8px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props > div > div {
  display: inline-block;
  width: 150px;
  height: 24px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props > div > div .dx-texteditor-input {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props > div > span {
  width: 130px;
  display: inline-block;
  position: relative;
  top: -5px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-pic {
  border-width: 1px;
  border-style: solid;
  float: right;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-pic .dxrd-wizard-customizelabel-pic-element {
  background: url(data:image/png;base64,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);
  width: 327px;
  height: 204px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-labelscounttext {
  padding-top: 10px;
  font-size: 14px;
  margin-top: 6px;
  float: right;
  text-align: right;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page {
  width: 646px;
  margin: auto;
  height: 345px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-pagesizetext-inline {
  display: none;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props {
  height: 55px;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize > span {
  top: -6px;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize > div {
  height: 26px;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props {
  line-height: 30px;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props > div > div {
  height: 26px;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props > div > span {
  top: -6px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-columns-page .dxrd-wizard-available-fields {
  float: left;
  width: 50%;
  padding-right: 15px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-columns-page .dxrd-wizard-available-fields .dxrd-wizard-available-fields-title {
  margin-bottom: 7px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-columns-page .dxrd-wizard-available-fields .dxrd-wizard-available-fields-title .dxrd-wizard-available-fields-checkbox {
  display: inline;
  margin-left: 10px;
  margin-right: 6px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-columns-page .dxrd-wizard-selected-fields {
  float: right;
  width: 50%;
  padding-left: 15px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-columns-page .dxrd-wizard-selected-fields .dxrd-wizard-selected-fields-title {
  margin-bottom: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-summary-options-page {
  height: 100%;
  padding: 0 0 20px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-content {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  height: 212px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-options-titles {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  padding-left: 190px;
  padding-top: 35px;
  padding-bottom: 5px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-options-titles > div {
  width: 82px;
  display: inline-block;
  text-align: center;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-columns {
  border-right-width: 1px;
  border-right-style: solid;
  width: 189px;
  min-height: 209px;
  float: left;
  padding-top: 15px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-columns .dxrd-wizard-summary-column-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  height: 30px;
  line-height: 30px;
  padding-left: 70px;
  padding-right: 5px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-options {
  padding-top: 15px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-options .dxrd-wizard-summary-options-values {
  height: 30px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-options .dxrd-wizard-summary-options-values > div {
  width: 82px;
  text-align: center;
  margin-top: 3px;
  display: inline-block;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-ignore-null-values {
  margin-top: 21px;
  margin-left: 70px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-tree {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-tree .dxrd-wizard-report-tree-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  padding-bottom: 5px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-tree .wizard-list-title {
  margin-top: 8px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dxrd-wizard-masterdetail-summary-options-title {
  position: absolute;
  height: 60px;
  width: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dxrd-wizard-masterdetail-summary-options-content {
  position: absolute;
  top: 60px;
  width: 100%;
  bottom: 34px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dxrd-wizard-masterdetail-summary-options-content .dxrd-wizard-list-container {
  position: absolute;
  top: 40px;
  bottom: 0px;
  width: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dxrd-wizard-list-title {
  position: absolute;
  height: 32px;
  padding-top: 10px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dxrd-wizard-ignore-null-values {
  position: absolute;
  bottom: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dxrd-wizard-ignore-null-values .dx-checkbox-text {
  line-height: 22px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dx-texteditor {
  height: 36px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dx-texteditor .dx-texteditor-input {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dx-texteditor .dx-placeholder {
  font-size: 14px;
  margin-top: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-texteditor {
  height: 36px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-texteditor .dx-texteditor-input {
  font-size: 14px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page .dx-texteditor .dx-placeholder {
  font-size: 14px;
  margin-top: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-list-title {
  position: absolute;
  height: 32px;
  padding-top: 10px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-title {
  position: absolute;
  height: 60px;
  width: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-content {
  position: absolute;
  top: 60px;
  width: 100%;
  bottom: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-content .dxrd-wizard-splitter {
  text-align: center;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-content .dxrd-wizard-splitter-action {
  float: none;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dx-fieldset .dx-field {
  padding: 4px 10px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dx-fieldset .dx-field .dx-field-label {
  font-size: 14px;
  width: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dx-fieldset .dx-field.dxd-back-primary {
  padding-top: 10px;
  padding-bottom: 10px;
  font-weight: bold;
  min-height: 0px;
  margin-bottom: 4px;
  margin-top: 4px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dx-fieldset .dx-field.dxd-back-primary .dx-field-label {
  padding: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dx-fieldset .dx-field.dxd-back-primary:first-child {
  margin-top: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dxrd-wizard-groups-item .dxrd-wizard-groups-actions {
  position: absolute;
  width: 120px;
  margin: 5px 0;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dxrd-wizard-groups-item .dxrd-wizard-groups-actions {
  right: 0;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dxrd-wizard-groups-item .dxrd-wizard-groups-actions {
  left: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dxrd-wizard-groups-item .dxrd-wizard-groups-actions > div {
  width: 28px;
  height: 28px;
  display: inline-block;
  padding: 2px;
  cursor: pointer;
  position: relative;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dxrd-wizard-groups-item .dxrd-wizard-groups-actions > div svg {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dxrd-wizard-groups-item .dxrd-wizard-groups-actions > div:last-child svg {
  width: 12px;
  height: 12px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dxrd-wizard-groups-item .dxrd-wizard-groups-choozer {
  width: calc(100% - 130px);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-groups-page .dxrd-wizard-masterdetail-groups-page-section-content .dxrd-wizard-groups-item .dxrd-wizard-groups-addgroup {
  text-decoration: underline;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-dataMember {
  border-width: 1px;
  border-style: solid;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-select-reportdata-page .dxrd-wizard-reportdata-column {
  width: 393px;
  height: 100%;
  display: inline-block;
  position: relative;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-select-reportdata-page .dxrd-wizard-reportdata-title {
  position: absolute;
  height: 30px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-select-reportdata-page .dxrd-wizard-add-queries-page {
  border-width: 1px;
  border-style: solid;
  position: absolute;
  top: 30px;
  bottom: 0px;
  width: 100%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-select-reportdata-page .dx-first-column {
  margin-right: 6px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items {
  margin-top: 23px;
  margin-bottom: 47px;
  display: inline-block;
  position: relative;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item {
  width: 105px;
  height: 135px;
  cursor: pointer;
  display: block;
  float: left;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item > div:first-child {
  margin-left: 11px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item .dxrd-report-layout-type-image {
  width: 83px;
  height: 98px;
  margin-top: 12px;
  background-repeat: no-repeat;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item .dxrd-report-layout-type-image-alignleft1 {
  width: 83px;
  height: 98px;
  margin-top: 12px;
  background-repeat: no-repeat;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFMAAABiCAYAAAAslO8IAAAEDUlEQVR4Xu2dMUskTRCG7253PW/vYw9FOD68H2PuD5CL/QH+AANDQwMDQzMNDMREE0HQwEgEEwURAwMx0UBBNuqbB7qW3sZhW1YbZuZ9oZjuqe2FeaypKUeb+vJOfW2clWp8cN+aZuMCjsG1Cmt76zTQ7NpbMeBUkAZworDvhf0orFvYzwZal+v3HCYMrLFKAfl3hMlGADWQHRa4UkkeZmcANJLlSEJ4MgmmYMKpbTn0LZjkhP+SYAomnCbKYLZ8ku0lwRTMnufVegtm2z+1ppJgCuaU59Uug0kZMJ0EUzDh1DWYcb7s+LpqJhXm0dFR3ew9MGc8r47lzSwwBVOqLEzBfHl5cYeHhxyH5o+Pj0Nj1O/3bT4YPzw8DHzHx8fMB+O7uztnsrmNb25uGIbzekTm2dkZYBzieHp6OuTb398fQMBMFxcXbm9vD3hD34Gurq7czs4OPj5n34GAio8flvn4XD1gAgMowDA4gQCBDwClPoMTivP48Vl0h9GIb2trC1+tcia3GRfGsdRHtMUiyvARYbGAhI9IjwT42FeryCRSyiITs8iMoy+KzFJfIyLT8p2BYx7nTIMT50x89sCJc2acT+OcaT6gVh5m/DAIb02eyozDfMdnmLOGcRhVgMFHSrDP8R1h5AM4/I7QR5pQnak6U6rj7+Z60SGY0vgwJcEUTME8ODhwS0tL7v7+3m1ubrrl5WUc4Zgjc8EcpefnZzc/P+8WFhbc3NycOzk5cej6+po55/EzF8zU6AQcERpqfX2d8xxdogRzcXGR6AOcOz8/x8FtzzkzIlgwR4lcCCzgAZXb2vIkY4O6uroqmJWUYAomebJ2NiRFpnKmYFIK8UR/enri9ZXb3t7GEY45MhfMUXp9faXscWtra25lZcVdXl5ancmc8/iZC2ZqdAKOCI1/M+I8x0QJ5sbGBtEHOHd7e+sQtz3nzIjgBOlvQMACHlC5rS1PMjaou7u7gll7CaZeDuda04iXw7nW1P7lcK41jXk5nGtNI14O51yj0qjepZGkl8MfKUWmYAom21LYHGAbnGwDQDjmyDzXmsrCtH/sZ2cEGwPYMBBuFOA8fua51lQSZrxpisiJt7RwnmPONZWGaRumbMtKuE3FjMjKtaayMMlRtg/IdqhZ/mJsgNiWl2tNZWBKgqnSSKWRSiOVRiqNVBqpNJIEU6WRSiOVRiqNVBqpNFJpJAlmuVQaqTRSaaTSSKWRSiOVRpJgCqZgCub4jULUKOQzWtiohc34zZXUXOkz236p7VfckG6UyeKGdOWtEkmsv3yZ9Luw/wubLexPA22W6/ccZjyXbmmrRIMZAZ30odzzyXYauA20aX/9Pc9jMgSJfVB7WbWXzdP4WI2P1ZI7tgw9z5tk6foHg3tlOPSloJMAAAAASUVORK5CYII=);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item .dxrd-report-layout-type-image-alignleft2 {
  width: 83px;
  height: 98px;
  margin-top: 12px;
  background-repeat: no-repeat;
  background-image: url(data:image/png;base64,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);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item .dxrd-report-layout-type-image-columnar {
  width: 83px;
  height: 98px;
  margin-top: 12px;
  background-repeat: no-repeat;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFMAAABiCAYAAAAslO8IAAADwUlEQVR4Xu2dsUocURSGk+yuMZuwQRFCMA9j7wNIwM4H8AF8AB/AB7CzsbLRxkoLKxGsBLGyEBsbbba6mQ/uP9y9ZMYJky1m5//hMPfOmV2YzzNnDseF8+Ef9bF3Vqn24D71zdoCzsENChtGG/XQdO+DHHBTkAK4VNjnwr4UNi7saw9tzP1HDksCK1ZNQP6uN9t7QAVyBIBQKSvCHJVAMylHEsLLjWAaJpyGyqF/g0lO+NYIpmHCaakK5iAm2UkjmIY5ibwGf4M5jG+tlUYwDXMl8hpWwaQMWG0E0zDhNBbMPF+OYl211gimYa5FXiPlzVYw397ewvn5OceZ/cvLy8waTadT7cv18/Nz6bu4uGBfrh8fH4OkvdYPDw9yad99mOj6+howAXG8urqa8Z2enpYQMOn29jacnJwAr/wO6e7uLhwfH+PjOn0HAio+/ljycd1iwAQGUIAhOIkAgQ8AtT7gpOI8fnyK7jQa8R0dHcnXeZgSjxk3xrHSR7TlIsrwEWGZgIRPkZ6nl9y3UJFJpFRFJqbIzKMvjcw6Xy8iU/lO4NjnOVNw8pyJTy+cPGdW5VPlTPmA2nmY+csgfTR5K7NO8x3XsAeGHtP0TY+PlKDr+I408gGcfkfqI010EaZlmIY5fxmmYRqmZZiGaZjb29sLZzNyZDpnGubZ2VnY3d0NT09P4fDwMOzt7QWUrjmyT9dcz+cuLy/r2no0ONQEVpMkXXNkn67V4acH0C2Yr6+vYXNzM2xtbYWNjY0Szv39PXvO42cfEE0Kncf4fIXU/KCRQVNEcNRM4bwaKeo06bw6Tt2CqegEEJGW6uDggPMcQ6qdnR2Bb9p0JtLyth/nOea90xR892ACh+gD0M3NTUA8xpyTKQLxAxIfUN+Rmspq66WtPJkiED8g8QG1ezDJf4ABHnB4dJUbWQvq/v5+QKzxCSowKkT+U69UXXzlRtaCqn9dsMYnqDz2HYBpGaZLI5dGLo1cGrk0cmnk0sgyTJdGLo1cGrk0cmnk0silkWWYhmmYiybD9M9j/PMYyzDd6HCjw40ONzrc6HCjw40OyzBdGrk0cmnk0silkUsjl0aWYdbLpZFLI5dGLo1cGrk0cmlkGaZhGqZhGqZhGqZhWoZpmIZpmP9zUIgHhbQfYeMRNvMaruThSu3HfnnsV+1Aunqz5QPp6kYlkli/xzLpR2E/C1sv7FcPbZ37jxzWIpdx5ahEwcyALsdQnsRkuwrcHtpqvP9J5LGcgsTmMF7W42VbDj724OP5j+T2SG4Pi28L7g8oFElCHTJtlwAAAABJRU5ErkJggg==);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item .dxrd-report-layout-type-image-justified {
  width: 83px;
  height: 98px;
  margin-top: 12px;
  background-repeat: no-repeat;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFMAAABiCAYAAAAslO8IAAAETklEQVR4Xu2dsUsjQRTG70ziebkjhyIch/ePWNvbp7e2T29vby02CmKTIgiCIFYiWIlgJRY2qdKkmtsP5lsmg8NG5mZvbvf74LEz89Y88ru3M3t5A/Ppg/rcOgsqHtxK2ywWsA+uU1jXWq+Fxu/e8QEvC5IAVwv7UtjXwvqFfWuh9fH9LYdVgiWrZUAOK0xWAZQgewhggpIszB6BvgdyxabwWjVMwbScupxD34OJOeF7NUzBtJxWQzA7dpIdLAVTMAeWV+c9mF27aq0vBVMw1y2vbghmv7CNapiCaTn1CdOfL3v2vWpzKZiCuWl59ThvRsGczWbm8vIS14X+dDpdaEPz+Zz9sv329lb6rq+v0S/bLy8vhmKf7efnZ7rY/79hUnd3dwBjIFxvb28XfOPxuIQAox4eHszFxQXglZ9BPT4+mtPTU/hwHz8DAlT48I9FH+5rBkzAABTAIBxHAAEfAAR9hOMK4/DDx+x2sxG+k5MT+hoBE8Jjhi+Ga9CHbKPcLIMPGeYJkOBjpvvTi+9rVGYiU0KZCWNm+tnnZWbQ14rM5HxHcOj7cybh+HMmfFxw/DkzNJ9yzqQPMRsB010M3EcTqzLa7nyHe9AHDD6m7koPH6YE3seVnpkPwO5nuD5ME9nDlARzeQmmYAqmJJiCKZhnZ2dNs38L8/X1NbXVFafRMAXz+PjY7O3tmfv7e3N4eGj29/fhc9u4or/Qxv34u/Pzcx9Aqjj5w3x6ejI7Oztmd3fXbG9vl3Bubm7Qxzj86GPcHB0dcRyGv/fBpYqTP0xmDQAhA1zfwcEBxnFdGB8Ohy4Q31LFyR8m4SArAGgymcCHxwtjNGYG/AACH6AGYCaIkztMzlkAA3iAg0eKcxbahDoajTCONnyEisfRh5YqjlZzvRoJpv4HVCvMq6srE6vM4jQRpmCyTo5qIndcsCLptnFF321zOw2KZXXFyR4mK42oGqICSTisXGKcVUuWdTnO8m5dcbKG6e/iQAb4NXaM4+pvVCCQmuPkD5M7OFhDd+vmNGYG/AACH6DWFSd7mJyzuDGBW2Y4Z6FNqNwnhDZ8hIrHse44+a/mWs0FUzUg1YDyM8EUzAT1HMH8e/UcwUxQzxHMyHqOYCap5wimVnPBVA1INSBINaDspBoQVR0nQm2pAVHVcSLUihoQVRknQm2qAVHVcSLUkhoQVR0nP5hazQVTr0YR8uNoe0yE3DjaHhMpP462x0TIj6PtMRHS9pjsJJh6NQor/atR4341ilf6V7D8X42iFP9q1MhfjcJK/2rUuF+Nwkr/aqTVXKt5lASzSZYappQ/TMGUBFMwBVMw4w8K0UEhKY6w0RE28Ycr6XCllMd+6dgv/0C6KpP5B9KFj0rExPrDrlg/C/tV2FZhv1toW/j+lsOm5dIPHpVImB7QNZvKAzvZbgBuC23Dfv+B5bHmgoQlOF5Wx8tGHnysg4/TH8mtI7l1WHwsuD9M8d35I6uw9gAAAABJRU5ErkJggg==);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item .dxrd-report-layout-type-image-outline1 {
  width: 83px;
  height: 98px;
  margin-top: 12px;
  background-repeat: no-repeat;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFMAAABiCAYAAAAslO8IAAAEF0lEQVR4Xu2dMUskTRCG7253PW/vYw9FOD68H2PuD5CL/QH+AANDQwMDQzMNDMREE0HQwEgEEwURA4NlEw0UZKO+eaBraRuHGRlsmJn3hWK6u7YX5rGmthxo6ssH9bV1lqvq4L61zaoCjsF1Mut667XQ7N47MeCyIA3gVGbfM/uRWT+zny20PvfvOUwZWGNVBuTfApMVADWQPTa4XEkeZm8CNJLlSEJ4uhRMwYRT13LoezDJCf+VgimYcJrKg9nxSXZQCqZgDjyvznswu/5Xa6YUTMGc8by6eTApA2ZLwRRMOPUNZpwve76umisL8+TkpGn2EZhznlfP8mYSmIIp1RamYL68vLjj42Oub+aPj49vxmg8Htt8Mh6NRhPf6ekp88n44eHBmWxu47u7O4bhvBmReXFxARiHuJ6fn7/xHR4eTiBgpqurK3dwcAC8yXeYbm5u3N7eHj4+Z9+BgIqPP5b5+FwzYAIDKMAwOIEAgQ8AuT6DE4p1/PgsusNoxLezs4OvUTmTx4wb45rrI9piEWX4iLBYQMJHpEcCfOxrVGQSKXmRiVlkxtEXRWaurxWRafnOwDGPc6bBiXOm5TvW45wZ59M4Z5oPqLWHGf8YhI8mv8qMw3zHZ5izh3EYVYDBR0qwz/EdYeQDOPyO0EeaUJ2pOlNq4v/metEhmFJ1mJJgCqZgHh0duZWVFTccDt329rZbXV3FEY65MhfMIj0/P7vFxUW3tLTkFhYW3NnZmUO3t7fMWcfPXDDLRifgiNBQm5ubrHN1JSWYy8vLRB/g3OXlJQ4ee9bMiGDBLBK5EFjAAyqPteVJxgZ1fX1dMGspwRRM8mTTTJFZXsqZgkmZxK/909MTr7bc7u4ujnDMlblgFun19ZWSyG1sbLi1tTV3fX1tNShz1vEzF8yy0Qk4IjT+r4l1riUlmFtbW0Qf4Nz9/T0OHnvWzIhgwSwSuRBYwAMqj7XlScYGdX9/XzAbIMEUzOovl1PtqQHM6i+XU+2pAcxqL5dT7qkBzOovl1PtqQHM6i+XU+1JALNNEkzB1MvhxkswBZNjLRwMsANSdoAgHHNlnmpPbWHawQAOAHBggMMD4UED1vEzT7WnljDjQ1dETnwkhnWuKffUGqYduLIjL+ExFzMiK9We2sIkR9k5IjvhZvmLsQHiWF+qPclgSoKp0kilkUojlUYqjVQaqTSSBFOlkUojlUYqjVQaqTRSaSQJZr5UGqk0Ummk0kilkUojlUaSYAqmYApm9UYhahTyGS1s1MKmenMlNVf6zLZfavsVN6QrMlnckC6/VSKJ9Zcvk35n9n9m85n9aaHNc/+ew5zn0s9tlWgwI6DTPpQHPtnOAreFNuvvf+B5TIcgsU9oL6v2shUaH6vxcZqW3GrJrWbxVcH9A7k7ZTgcyFAAAAAAAElFTkSuQmCC);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item .dxrd-report-layout-type-image-outline2 {
  width: 83px;
  height: 98px;
  margin-top: 12px;
  background-repeat: no-repeat;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFMAAABiCAYAAAAslO8IAAAEH0lEQVR4Xu2dMUscURDHk9ydMZdwQRFCMB/G3g8gqf0AfgALS0sLC0s7LSzERhtB0MJKBBsFEQsLsdFCQa7a7G95c7x7ZNkXFtfs7n9g2Pfe3DvYn7Pz5laG+fCP8rF1mivlwX1qm5YFHILrpNp12muh2r13QsCxIA3gRKqfU/2Saj/Vry3UPvfvOEwYWGMVA/J3gUoLgBrIHhuSXJE4mL0R0EAsRuLCk1EwBRNOXYuhf4NJTPgWBVMw4TSRB7PjguwgCqZgDhyvzt9gdt2pNRUFUzCnHK9uHkzSgOkomIIJp77BDONlz+VVM1EwBXPG8epZ3CwF8+joqGlaAUxJrWAK5svLS3J4eMh1bP74+Dg2RobDoc1H44eHh5Ht+PiY+Wh8d3eXmNjcxjc3Nwz9eTM88+zsLAODcD09PR2z7e/vjyCgJhcXF8ne3h7wxr4Dubq6SnZ2drDxOfsOBKjY+GOZjc81AyYwgAIMg+MJILABINdmcHxhHTs2827fG7FtbW1ha1TM5DHjxrjm2vC2UPAybHhYIEDCZp4ehpfQ1ijPxFPyPBM1zwy9L/DMXFsrPNPinYFjHsZMgxPGTGx24IQxM4ynYcw0G1BrDzM8DPxHk1OZsR/v+Axz9thj6p/02AgJ9jn/pAcsgP3v8G2EiVrClLw/TMHUb3N5pmKmYM7NzTVN3xdmg6SRMAXz4OAgWVpaSu7v75PNzc1keXkZw9iYK/MCEczn5+dkfn4+WVhYyNZPTk4S5Pr6mjnr2LN5gQimeSdreKgv6+vrrGfXSBHMxcVFvC9bPz8/x8Bjz9pI8eACEUxiIbCAB1Qea4uTjA3q6upqrWDqNBdMwYxS/QKSNB6mYJImcdo/PT1l7wy3t7cx+GOuzAWzSF5fX7OUaG1tLVlZWUkuLy8tB2XOOnbmghnrnYDDQ8NfTaxzjRTB3NjYwPsycLe3txh47FkzxYMFM+Z/RcACHlB5rC1OMjaou7u7gtkIEUzBLPFCuYI9NYNZ4oVyBXtqCTP+hXKVe2oMM/6FclV7agezxAvlCvZUALM9IpiCqZfDjRfBFEzKWigMsAIpKyDwx1yZV7WntjCtMIACAAoGKB7wCw1Yx868qj21hBkWXeE5YUkM61yr3FNrmFZwZSUvfpmLKZ5V1Z7awiRGWR2RVbhZ/GJsgCjrq26PTnOd5ipdkQimknYl7UralbQraVfSrqRd8t/BVNIuEUwl7UralbQraVfSrqRdSXv7REm7knZ5pkQwBVMwBbN8oxA1CnmLFjZqYVO+uZKaK71l2y+1/Qob0hWpNGxIl98qkcD63Z1YP1L9mepsqr9aqLPcv+Mw47j0c1slGswA6KRz5YELttPAbaFOu/sfOB6TPkj0DdrLqr1sicbHanxcTUtuteRWs/iy4P4A3ZkaVoufd+IAAAAASUVORK5CYII=);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item .dxrd-report-layout-type-image-stepped {
  width: 83px;
  height: 98px;
  margin-top: 12px;
  background-repeat: no-repeat;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFMAAABiCAYAAAAslO8IAAADq0lEQVR4Xu2dv4oiQRDG79Y/53mHx4pwHHsPY+4DLBfvA+wD+AA+gA9gZrKRiSZGbmAkgpEgRhuIiYmbbNQ339AftDM3OEfTwTjfB8V0T0nD/LampqAW6tN/6nPpLFP+4O7KZn6A0+AqkVWt1UpofPZKEnBekARYj+xLZF8ja0b2rYTWxPNbDnWCJas8IP9cMdkVoARZAwCTKcnCrDlAUyDvbAg3csEUTHCqMoemYNqc8D0XTMEEp3oWzIpNsq1cMAWzZXlV/gWzar9a97lgCua95VXNgokyoJ0LpmCCU5Mwk/myZuuqTi6YgtmxvGrMm14w39/fzXw+x/VifzqdLtbQx8cH91yb4/FIn1ksFvGe67e3N0Nxz/V+v6eL+2LDpFarVQwGwnW5XF74ptMpIcRGbTYbM5lMYng8g9put+bl5QU+/I5nQIAKH/5Y9OGM24AJGIACGITjCCDgA4BMH+G4wn344WN0u9EI33g8pq/wMCm8ZngwXDN9iDbKjTL4EGEJARJ8jPRkekn6bioyESlZkQljZCajLxGZmb5SRCbzHcFhn8yZhJPMmfDxg5PMmVn5lDmTPkAtOMz0x8B9NfFVxpr5jq8m9oDB19T90sOHlMDf4Qw38gHYPcP1IU0UEaYkmIIZXoIpmIIpCaZgCuZsNjPPz8/mcDiY0Whk+v2+gdw1rthDBT4zPMzz+Wx6vZ55fHw03W7XvL6+Gmi322GP+/DHe6jAZ4aHyUgCOESTq+FwiPvxlSr4meFhPj09IVJicOv12kB4nXCPhsiACnpmeJjMOYAFeACA14X5B2sCGAwGBiramSqNVBoJJvLPrZkiU6+5YKY7j+jPsIfNHo+7xhX7KxJM9m7Qh0FPh70Z9oJwn30gwczfM0eEprqWuI9rTgkme+LsSrqdSBoi+IoEE7mQrV7+EwLzJNaEulwuBVMSTMH0LLcE07/cEszw5ZZg+pdbgulfbgmmSiNJMP3LLcH0L7cE07/cEszw5ZZg+pdbgqnSKLwEU6WRSiOVRoKp0kilkSSYaqipoaaGmhpqaqgJphpqkmCmJZhqqKmhpoaaYKqhpoaaJJiCqdJIpZFKI5VGKo1UGklFhimYguk/KESDQkKMsNEIG//hShquFG7sl8Z+pQfSXTFZaiBd9qhEJNYftkz6GdmvyB4i+11Ce8DzWw4dy6WZOSqRMBNAGzaUWzbZtgG3hNa2z9+yPBouSFiA8bIaL+s5+FiDj8OP5NZIbg2L9wX3F12uUiuc0M0yAAAAAElFTkSuQmCC);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item .dxrd-report-layout-type-image-tabular {
  width: 83px;
  height: 98px;
  margin-top: 12px;
  background-repeat: no-repeat;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFMAAABiCAYAAAAslO8IAAAEC0lEQVR4Xu2dMUokQRSGd50Z151dZlGEZXEPY+4BZGMP4AE8gAfwAGYmRiaaGGlgJIKRIEYGYmIyJhPV9tfUD2VhYy/NUOz0/+DRVfXaLvrbV9X/8gbq0z/a5955o3UHt9Q37wo4BzeofBh91EPXuw9ywG1BCuBy5V8q/1r5uPJvPfQx7x85LAusWLUB+ecDt38AVCBHAAiNZoswRwL6HsilmMIrrWAaJpyG2kPfg8me8L0VTMOE03ITzEHcZCetYBrmJPIavAdzGL9aq61gGuZq5DVsgokMWGsF0zDhNM5hSqCPoq5abwXTMNcjr5GEfCeYr6+v4fz8nOub/svLy5s2NpvN1Fc7PD8/KxYuLi7qvtqPj49Bpr7aDw8PCqn//8PErq+vazAY16urqzex09NTQahddnt7G05OTmp4eobs7u4uHB8fE+M+PQMDKjH+sRTjvsWACQygAENwEgMEMQA0xgQnNcaJE1N2p9lI7OjoSLGFgImxzHgxro0xsi03sowYGZYZkIgp0/PtJY8tVGaSKU2ZiSsz8+zLMrMx1ovM1H4ncPTzPVNw8j2TmD44+Z7ZtJ9qz1QMqAsBM/0YpEuTrzLtdL/jHvrA0DJNv/TE2BJ0H89IMx/A6TPSGNvEfwDTZpiGWcAM0zAN02aYhmmYZ2dnYXd3Nzw9PYXDw8Owt7cXsLTNlX7a5n7+7vLyUu36WTc3N3X7/v6+xJxlYU6n07C1tRW2t7fD5uZmDQcDBn3GiQsO/3vROM7fY/v7+3oOIErNWRamMgVAZEtqBwcHjNfX1HZ2dgQhB8Q4WVNqzvIwgSMQLBkMIIzJlQ3EgUAMqJiWqMYBUWDO8jCBAADgAYclo32KtqCypDDaxASVJQgE2gClnwAqPqelkaWRYbI8Fs6dmV7mhplXHqnPqIatGk/a5ko/besHCpQf1OZZlCr0Q4UScxaFqdoNdRhqOsBJa0GMqw6kQpnG00omJVs9BxCl5iwGM6+Lky151ZJxrnnpVxByQIyTNYXmLA9TNXFVJdNKpFzZQBwIxICaln01DogSc5aHCQSVevUjBO1TtAWVJYXRJiaoKt/SBih9ASowZwGYNsO0NLI0sjSyNLI0sjSyNLIZpqWRpZGlkaWRpZGlkaWRzTAbzNLI0sjSyNLI0sjSyNLIZpiWRpZGlkaWRpZGlkaWRjbDtDSyNLI0sjSyNLI0sjSyGaZhGuaCmWEapmHaDNMwDdMwux8U4oNC5nGEjY+w6X64kg9XmuexXz72Kz+Q7iO35wfSNR+VyMb6I8qkn5X/qnyj8t899A3eP3JYj1zGjUclCmYGdCWm8iRutmvA7aGvxfefRB4rKUh8DsfL+njZjgcf++Dj+R/J7SO5fVh8V3B/Aey2IC+ZWkNFAAAAAElFTkSuQmCC);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item .dxrd-report-layout-type-text {
  text-align: center;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dx-radiogroup {
  margin-bottom: 27px;
  margin-left: 34px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dx-widget.dx-checkbox {
  margin-top: 41px;
  position: absolute;
  text-align: left;
  left: 34px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-five-items .dx-radiogroup {
  margin-left: 17px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-report-layout-type-page .dxrd-report-layout-type-five-items .dx-widget.dx-checkbox {
  left: 17px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page {
  padding: 50px 30px 20px 90px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container {
  height: 100%;
  width: 410px;
  padding-top: 6px;
  float: left;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image {
  width: 371px;
  height: 231px;
  padding-top: 48px;
  padding-left: 43px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background-image: url(data:image/png;base64,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);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image div {
  vertical-align: top;
  -webkit-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0);
  padding: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image > div {
  text-align: left;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.bold .title {
  font-family: 'Times New Roman';
  font-size: 20pt;
  font-weight: bold;
  color: maroon;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.bold .caption {
  font-family: Arial;
  font-size: 10pt;
  font-weight: bold;
  color: maroon;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.bold .data {
  font-family: 'Times New Roman';
  font-size: 10pt;
  color: black;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.casual .title {
  margin-left: 3px;
  font-family: Tahoma;
  font-size: 24pt;
  font-weight: bold;
  color: teal;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.casual .caption {
  font-family: Arial;
  font-size: 10pt;
  font-weight: bold;
  color: black;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.casual .data {
  font-family: Arial;
  font-size: 10pt;
  color: black;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.compact .title {
  font-family: 'Times New Roman';
  font-size: 21pt;
  color: black;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.compact .caption {
  font-family: 'Times New Roman';
  font-size: 10pt;
  color: black;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.compact .data {
  font-family: Arial;
  font-size: 9pt;
  color: black;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.corporate .title {
  font-family: 'Times New Roman';
  font-size: 20pt;
  font-weight: bold;
  font-style: italic;
  color: navy;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.corporate .caption {
  font-family: 'Times New Roman';
  font-size: 11pt;
  font-weight: bold;
  font-style: italic;
  color: navy;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.corporate .data {
  font-family: Arial;
  font-size: 8pt;
  color: black;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.formal .title {
  font-family: 'Times New Roman';
  font-size: 24pt;
  color: black;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.formal .caption {
  font-family: 'Times New Roman';
  font-size: 10pt;
  font-weight: bold;
  color: black;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.formal .data {
  font-family: 'Times New Roman';
  font-size: 8pt;
  color: black;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-items {
  margin: 55px 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-treelist-item {
  padding: 6px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-treelist-item .dx-treelist-caption {
  height: 23px;
  line-height: 23px;
  width: -webkit-fill-available;
  width: -moz-available;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-treelist-item .dx-treelist-collapsedbutton {
  height: 22px;
  width: 22px;
  min-width: 22px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-treelist-item .dx-treelist-image {
  display: none;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-field-value:not(.dx-switch):not(.dx-checkbox):not(.dx-button),
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-field-value-static {
  width: 75%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-page-treelist-accordion-style .dx-treelist .dx-editors .dx-fieldset .dx-field {
  padding-top: 3px;
  padding-bottom: 2px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-crosstab-select-dataMembers-page .dx-treelist .dx-treelist-item.dx-treelist-field-item-content .dx-treelist-image {
  display: inline-block;
  padding: 6px 0;
  margin-left: 13px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-crosstab-select-dataMembers-page .dx-treelist .dx-treelist-item.dx-treelist-field-item-content .dx-treelist-selectedcontent .dx-add-queries-page-checkbox {
  display: none;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-crosstab-select-dataMembers-page .dx-treelist .dx-treelist-item.dx-treelist-field-item-content .dx-treelist-selectedcontent .dx-treelist-text-with-checkbox {
  padding-left: 0;
  padding-right: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-crosstab-setlayout-page.dxrd-wizard-crosstab-droppable-page {
  outline-width: 2px;
  outline-style: solid;
  outline-offset: -2px;
  outline-color: rgba(10, 222, 93, 0.5);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-wizard-crosstab-setlayout-page.dxrd-wizard-crosstab-droppable-page .dxrd-wizard-list {
  border-color: rgba(10, 222, 93, 0.5);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview {
  position: absolute;
  left: 21px;
  width: 270px;
  top: 20px;
  padding: 10px;
  bottom: 60px;
  text-align: center;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-preview-page {
  position: absolute;
  background-color: white;
  border: 1px solid black;
  margin: auto;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-preview-page > div {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-preview-page .dxrd-wizard-preview-margin-top {
  border-top: 1px dashed grey;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-preview-page .dxrd-wizard-preview-margin-right {
  border-right: 1px dashed grey;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-preview-page .dxrd-wizard-preview-margin-bottom {
  border-bottom: 1px dashed grey;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-preview-page .dxrd-wizard-preview-margin-left {
  border-left: 1px dashed grey;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content {
  position: absolute;
  left: 321px;
  right: 21px;
  top: 20px;
  bottom: 20px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dx-fieldset .dx-field {
  height: 36px;
  padding-left: 10px;
  padding-top: 4px;
  padding-bottom: 4px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dx-fieldset .dx-field .dx-field-label {
  padding-right: 5px;
  font-size: 14px;
  width: calc(15% - 3px);
  position: absolute;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dx-fieldset .dx-field .dx-field-label {
  left: 0;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dx-fieldset .dx-field .dx-field-label {
  right: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dx-fieldset .dx-field .dx-field-value {
  height: 36px;
  position: absolute;
  width: auto;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dx-fieldset .dx-field .dx-field-value .dx-texteditor-input {
  font-size: 14px;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dx-fieldset .dx-field .dx-field-value {
  right: 0px;
  left: calc(15% - 3px);
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dx-fieldset .dx-field .dx-field-value {
  left: 0px;
  right: calc(15% - 3px);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dx-fieldset.dxrd-page-setup-long-group {
  padding-left: 10px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dx-fieldset.dxrd-page-setup-long-group .dx-field {
  padding-right: 3px;
  height: 36px;
  padding-left: 10px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset {
  width: 100%;
  display: block;
  vertical-align: top;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset {
  padding-left: 10px;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset {
  padding-right: 10px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset.dxrd-page-setup-pagemode {
  width: 248px;
  margin-top: 4px;
  position: absolute;
  top: 0px;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset.dxrd-page-setup-pagemode {
  right: 0;
  left: auto;
  padding-left: 0px;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset.dxrd-page-setup-pagemode {
  right: auto;
  left: 0;
  padding-right: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset .dx-field {
  padding-right: 5px;
  height: 34px;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset .dx-field .dx-field-value {
  right: 256px;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset .dx-field .dx-field-value {
  left: 256px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset .dxrd-page-setup-landscape-item {
  width: 120px;
  height: 120px;
  margin-left: 3px;
  display: inline-block;
  position: relative;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset .dxrd-page-setup-landscape-item .dxrd-page-setup-landscape-item-icon {
  width: 52px;
  height: 52px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-group-caption {
  font-weight: bold;
  padding-top: 10px;
  padding-bottom: 10px;
  margin-bottom: 8px;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-group-caption {
  margin-right: 3px;
  padding-left: 10px;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-group-caption {
  margin-left: 3px;
  padding-right: 10px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting {
  min-height: 26px;
  height: 124px;
  position: relative;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting-page-margins {
  height: 84px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting-page-margins .dxrd-group-fieldset .dx-field .dx-field-label {
  width: calc(15% - 3px);
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting-page-margins .dxrd-group-fieldset .dx-field .dx-field-value {
  right: calc(50% + 15px);
  left: calc(15% - 3px);
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting-page-margins .dxrd-group-fieldset .dx-field .dx-field-value {
  left: calc(50% + 15px);
  right: calc(15% - 3px);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting-page-margins .dxrd-group-fieldset.dxrd-group-fieldset-left {
  position: absolute;
  top: 0px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting-page-margins .dxrd-group-fieldset.dxrd-group-fieldset-right {
  position: absolute;
  top: 0px;
}
.dx-ltr .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting-page-margins .dxrd-group-fieldset.dxrd-group-fieldset-right {
  left: 50%;
}
.dx-rtl .dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting-page-margins .dxrd-group-fieldset.dxrd-group-fieldset-right {
  right: 50%;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-pagemode-compact {
  display: none;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme {
  letter-spacing: -3px;
  position: absolute;
  top: 0;
  text-align: center;
  margin: auto;
  left: 0;
  right: 0;
  height: auto;
  max-height: 288px;
  bottom: 0;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile {
  width: 120px;
  height: 76px;
  line-height: 75px;
  display: inline-block;
  margin: 10px;
  vertical-align: top;
  text-align: center;
  border-radius: 3px;
  letter-spacing: normal;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.8);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile .dxrd-page-color-scheme-tile-content {
  display: block;
  height: 100%;
  position: relative;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile .dxrd-page-color-scheme-tile-content .dxrd-page-color-scheme-tile-selected {
  background: transparent;
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-width: 2px;
  border-radius: 3px;
  border-style: solid;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile .dxrd-page-color-scheme-tile-content .dxrd-page-color-scheme-tile-content-title {
  right: 0px;
  left: 0px;
  position: absolute;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile .dxrd-page-color-scheme-tile-content .dxrd-page-color-scheme-tile-content-custom .dxrd-page-color-scheme-tile-content-title {
  right: 46px;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile .dxrd-page-color-scheme-tile-content .dxrd-page-color-scheme-tile-content-custom .dxrd-page-color-scheme-tile-content-edit {
  position: absolute;
  right: 0;
  height: 100%;
  width: 46px;
  background-color: rgba(0, 0, 0, 0.1);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile .dxrd-page-color-scheme-tile-content .dxrd-page-color-scheme-tile-content-custom .dxrd-page-color-scheme-tile-content-edit:hover {
  background-color: rgba(0, 0, 0, 0.2);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile .dxrd-page-color-scheme-tile-content .dxrd-page-color-scheme-tile-content-custom .dxrd-page-color-scheme-tile-content-edit.dxrd-page-color-scheme-tile-content-edit-active {
  background-color: rgba(0, 0, 0, 0.3);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile .dxrd-page-color-scheme-tile-content .dxrd-page-color-scheme-tile-content-custom .dxrd-page-color-scheme-tile-content-edit.dxrd-page-color-scheme-tile-content-edit-active:hover {
  background-color: rgba(0, 0, 0, 0.3);
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile .dxrd-page-color-scheme-tile-content .dxrd-page-color-scheme-tile-content-custom .dxrd-page-color-scheme-tile-content-edit .dxrd-page-color-scheme-tile-content-edit-icon {
  width: 22px;
  height: 22px;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
  position: absolute;
}
.dxrd-wizard .dxrd-wizard-content .dxrd-page-color-scheme .dxrd-page-color-scheme-tile .dxrd-page-color-scheme-tile-content .dxrd-page-color-scheme-tile-content-custom .dxrd-page-color-scheme-tile-content-edit .dxrd-page-color-scheme-tile-content-edit-icon svg {
  position: absolute;
  top: 0;
  left: 0;
}
.dxrd-wizard .dxrd-masterDetail-editor-complete-wizard {
  height: 286px;
}
.dxrd-master-detail-report-wizard.dxrd-wizard .dxrd-masterDetail-editor-complete-wizard {
  height: 390px;
}
.dx-popover-wrapper .dx-list .dx-list-item {
  border-top: none;
}
.dx-popover-wrapper .dx-list .dx-list-item .dx-text-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  text-align: center;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}
.dx-overlay-wrapper .dxrd-wizard-customizelabel-select-customtemplate .dx-list-item-content {
  padding: 7px 5px;
}
.dx-overlay-wrapper .dxrd-wizard-customizelabel-select-customtemplate .dxrd-wizard-customizelabel-select-template {
  margin: 0;
  padding: 0;
  position: relative;
}
.dx-overlay-wrapper .dxrd-wizard-customizelabel-select-customtemplate .dxrd-wizard-customizelabel-select-template > span {
  display: inline-block;
  font-size: 14px;
}
.dx-overlay-wrapper .dxrd-wizard-customizelabel-select-customtemplate .dxrd-wizard-customizelabel-select-template > span:first-child {
  width: 100px;
}
.dx-overlay-wrapper .dxrd-wizard-customizelabel-select-customtemplate .dxrd-wizard-customizelabel-select-template > span:last-child {
  position: absolute;
  width: 92px;
}
.dxrd-wizard-customizelabel-select-customtemplate .dxrd-wizard-customizelabel-select-template > span {
  text-align: left;
}
.dxrd-wizard-customizelabel-select-customtemplate .dxrd-wizard-customizelabel-select-template > span:last-child {
  right: 10px;
}
.dx-rtl .dxrd-wizard-customizelabel-select-customtemplate .dxrd-wizard-customizelabel-select-template > span {
  text-align: right;
}
.dx-rtl .dxrd-wizard-customizelabel-select-customtemplate .dxrd-wizard-customizelabel-select-template > span:last-child {
  left: 10px;
  right: auto;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dx-button {
  margin: 0;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-columns {
  border-left-width: 1px;
  border-left-style: solid;
  float: right;
  border-right: none;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-columns .dxrd-wizard-summary-column-name {
  padding-left: 5px;
  padding-right: 70px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-summary-options-titles {
  padding-right: 190px;
  padding-left: 0px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-summary-options-page .dxrd-wizard-ignore-null-values {
  margin-left: 0px;
  margin-right: 70px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-right-panel {
  float: right;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-left-panel {
  float: right;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-splitter {
  float: right;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-splitter-action {
  transform: rotateY(180deg);
}
.dxrd-wizard .dx-rtl .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item {
  float: right;
}
.dxrd-wizard .dx-rtl .dxrd-report-layout-type-page .dxrd-report-layout-type-items .dxrd-report-layout-type-item > div:first-child {
  margin-right: 11px;
}
.dxrd-wizard .dx-rtl .dxrd-report-layout-type-page .dx-radiogroup {
  margin-right: 34px;
}
.dxrd-wizard .dx-rtl .dxrd-report-layout-type-page .dx-widget.dx-checkbox {
  right: 34px;
  text-align: right;
}
.dxrd-wizard .dx-rtl .dxrd-report-layout-type-page .dxrd-report-layout-type-five-items .dx-radiogroup {
  margin-right: 17px;
}
.dxrd-wizard .dx-rtl .dxrd-report-layout-type-page .dxrd-report-layout-type-five-items .dx-widget.dx-checkbox {
  right: 17px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-report-style-page {
  padding: 50px 90px 20px 30px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container {
  float: right;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image {
  padding-right: 43px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image.casual .title {
  margin-right: 3px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-content .dxrd-wizard-report-style-page .dxrd-wizard-report-style-image-container .dxrd-wizard-report-style-image > div {
  text-align: right;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-radiounit {
  float: left;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-radiounit .dx-radio-value-container:first-child {
  padding-right: 0px;
  padding-left: 10px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize {
  float: right;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-pagesizetext {
  margin-right: 135px;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props {
  float: right;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-pic {
  float: left;
}
.dxrd-wizard .dx-rtl .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-labelscounttext {
  float: left;
  text-align: left;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-page-setup-content {
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview {
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  width: auto;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-preview-reporttitle {
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-preview-pagecontainer {
  width: 100%;
  height: calc(100% - 56px);
  position: relative;
  margin-top: 56px;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-rows-container {
  top: 40px;
  position: absolute;
  width: 100%;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-rows-container .dxrd-wizard-preview-row {
  height: 20px;
  width: 100%;
  letter-spacing: -4px;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-page-setup-page .dxrd-wizard-preview .dxrd-wizard-rows-container .dxrd-wizard-preview-row .dxrd-wizard-preview-column {
  display: inline-block;
  overflow: hidden;
  text-align: left;
  padding: 2px 0px 0px 2px;
  height: 20px;
  outline: 1px solid;
  letter-spacing: normal;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  height: 35px;
  overflow: hidden;
  position: relative;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos.dxrd-wizard-field-infos-title {
  display: flex;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos.dxrd-wizard-field-infos-title .dxrd-wizard-field-infos-title-image {
  margin: 0 8px;
  width: 24px;
  height: 24px;
  padding: 6px 0;
  background-position: center;
  box-sizing: content-box;
  position: relative;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos.dxrd-wizard-field-infos-title .dxrd-wizard-field-infos-title-content {
  font-weight: bold;
  font-size: 14px;
  padding: 7px 9px 8px;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos.dxrd-wizard-field-infos-paragraf .dxrd-wizard-field-infos-fields,
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos.dxrd-wizard-field-infos-paragraf .dxrd-wizard-field-infos-values,
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos.dxrd-wizard-field-infos-paragraf .dxrd-wizard-field-infos-removebtn {
  font-size: 14px;
  padding: 7px 9px 8px;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos.dxrd-wizard-field-infos-paragraf .dxrd-wizard-field-infos-removebtn {
  height: 34px;
  border-right: 0px;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-fields {
  border-right-width: 1px;
  border-right-style: solid;
  width: 250px;
  position: absolute;
  left: 0px;
  height: 35px;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-fields .dx-texteditor.dx-selectbox {
  border: 1px solid transparent;
  height: 34px;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-values {
  border-right-width: 1px;
  border-right-style: solid;
  left: 250px;
  position: absolute;
  right: 28px;
  height: 35px;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-values .dx-texteditor.dx-selectbox {
  border: 1px solid transparent;
  height: 34px;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-removebtn {
  top: 0;
  right: 0;
  height: 34px;
  width: 28px;
  border-right: 0px;
  position: absolute;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-removebtn .dx-button {
  padding: 4px;
  border-radius: 0px;
  height: 35px;
  width: 28px;
  border: 1px solid transparent;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-removebtn .dx-button .dx-icon {
  width: 12px;
  height: 12px;
  font-size: 12px;
  background-size: 12px 12px;
  line-height: 14px;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dx-state-active {
  z-index: 1;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dx-state-focused {
  z-index: 1;
}
.dxrd-report-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dx-state-focused.dx-texteditor.dx-selectbox {
  border-width: 1px;
  border-style: solid;
  height: 34px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-disabled-content {
  margin-left: 10px;
  margin-right: 10px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-masterdetail-select-reportdata-page .dxrd-wizard-reportdata-title {
  display: none;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-masterdetail-select-reportdata-page .dxrd-wizard-add-queries-page {
  top: 0px;
  z-index: 100;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-masterdetail-summary-options-page .dxrd-wizard-masterdetail-summary-options-content {
  top: 0px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-masterdetail-summary-options-page .dxrd-wizard-masterdetail-summary-options-content .dxrd-wizard-list-container {
  top: 0px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-wizard-masterdetail-groups-page {
  border-width: 1px;
  border-style: solid;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page .dxrd-wizard-selectlabelproduct-page,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page .dxrd-wizard-customizelabel-page {
  right: auto;
  bottom: auto;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group.dxrd-page-color-setup-group {
  margin-top: 16px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group.dxrd-page-color-setup-group .dxrd-page-setup-setting {
  max-width: 704px;
  margin: auto;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset.dxrd-page-setup-pagemode {
  display: none;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-pagemode-compact {
  display: block;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-group-fieldset {
  right: 3px;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting {
  height: 170px;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting.dxrd-page-setup-setting-page-margins .dxrd-group-fieldset.dxrd-group-fieldset-left,
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-page-setup-content .dxrd-page-setup-group .dxrd-page-setup-setting.dxrd-page-setup-setting-page-margins .dxrd-group-fieldset.dxrd-group-fieldset-right {
  position: relative;
  width: 100%;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-group-fieldset .dx-field .dx-field-value {
  right: 0;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-group-fieldset.dxrd-group-fieldset-left,
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-group-fieldset.dxrd-group-fieldset-right {
  right: 3px;
  left: 0;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-group-fieldset .dx-field .dx-field-value {
  left: 0;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-group-fieldset.dxrd-group-fieldset-left,
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-report-page-tile .dxrd-report-page-tile-content .dxrd-wizard-page.dxrd-page-setup-page .dxrd-group-fieldset.dxrd-group-fieldset-right {
  left: 3px;
  right: 0;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-select-reportdata-page .dxrd-wizard-reportdata-column {
  width: 100%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page {
  border-width: 1px;
  border-style: solid;
  width: 100%;
  height: 100%;
  padding: 12px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list {
  width: 400px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list > div {
  height: 36px;
  line-height: 36px;
  display: block;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list > div:not(.dxrd-wizard-selectlabelproduct-list-label) {
  margin-top: -42px;
  position: absolute;
  width: calc(100% - 156px);
}
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list > div:not(.dxrd-wizard-selectlabelproduct-list-label) {
  right: 12px;
}
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list > div:not(.dxrd-wizard-selectlabelproduct-list-label) {
  left: 12px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectlabelproduct-list .dxrd-wizard-selectlabelproduct-list-label {
  width: 130px;
  position: relative;
  margin-top: 4px;
  margin-bottom: 4px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-selectlabelproduct-page .dxrd-wizard-selectproduct-props > div > span:first-child {
  width: 130px;
  height: 24px;
  padding-top: 4px;
  padding-bottom: 4px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props {
  width: 60%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props > div {
  height: 36px;
  margin: 4px 0;
  position: relative;
}
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-pagesizetext {
  margin-left: 24%;
}
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-pagesizetext {
  margin-right: 24%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize {
  width: 60%;
  height: 42px;
  position: relative;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize > div,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props > div > div {
  height: 36px;
  position: absolute;
  width: calc(60% - 10px);
}
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize > div,
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props > div > div {
  right: 10px;
}
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize > div,
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props > div > div {
  left: 10px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize > span,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props > div > span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  position: absolute;
  top: 0;
  width: 40%;
  height: 36px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-radiounit,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-pic,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-labelscounttext {
  position: absolute;
  width: calc(40% - 10px);
  float: unset;
  overflow: hidden;
}
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-radiounit,
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-pic,
.dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-labelscounttext {
  right: 10px;
}
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-radiounit,
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-pic,
.dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-labelscounttext {
  left: 10px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-radiounit {
  top: 15px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-pic {
  top: 70px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-labelscounttext {
  top: 275px;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-props-container .dxrd-wizard-customizelabel-props,
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-select-papersize {
  width: 100%;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-radiounit,
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-pic,
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-labelscounttext {
  display: none;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-pagesizetext-inline {
  display: block;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-ltr.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-pagesizetext {
  margin-left: 40%;
}
.dx-designer-viewport:not(.dx-designer-viewport-lg):not(.dx-designer-viewport-xl) .dx-rtl.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-customizelabel-page .dxrd-wizard-customizelabel-head-props .dxrd-wizard-customizelabel-pagesizetext {
  margin-right: 40%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dxrd-wizard-field-infos .dxrd-wizard-field-infos-fields {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  width: 45%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-masterdetail-summary-options-page .dxrd-wizard-field-infos .dxrd-wizard-field-infos-values {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  left: 45%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-section-crosstab .dxrd-report-page-tile .dxrd-report-page-tile-content {
  top: 0;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-crosstab-select-dataMembers-page .dxrd-wizard-list-container,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-crosstab-setlayout-page .dxrd-wizard-list-container {
  height: 100%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-crosstab-select-dataMembers-page .dxrd-wizard-list-container .dxrd-wizard-list,
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-crosstab-setlayout-page .dxrd-wizard-list-container .dxrd-wizard-list {
  height: 100%;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-field-infos {
  height: 36px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-fields {
  height: 36px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-fields .dx-texteditor {
  border: 1px solid transparent;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-values {
  height: 36px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-values .dx-texteditor.dx-selectbox {
  height: 36px;
}
.dx-fullscreen-wizard .dxrd-wizard .dxrd-wizard-content .dxrd-wizard-field-infos .dxrd-wizard-field-infos-removebtn {
  height: 36px;
}
.dx-designer .dxrd-localization-editor {
  font-weight: normal;
  font-size: 14px;
  font-family: 'Helvetica Neue', 'Segoe UI', Helvetica, Verdana, san-serif;
  line-height: 1.35715;
  position: absolute;
  top: 0;
  bottom: 0;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
  z-index: 1001;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-property-grid-search-collapsed .dx-property-grid-search-box {
  display: none;
}
.dx-designer .dxrd-localization-editor.dx-ltr {
  left: 50px;
  right: auto;
}
.dx-designer .dxrd-localization-editor.dx-ltr .dxrd-left-localization-panel .dxrd-localization-panel-header .dxrd-localization-panel-title {
  padding-left: 30px;
}
.dx-designer .dxrd-localization-editor.dx-ltr .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset {
  padding: 20px 30px 22px 16px;
}
.dx-designer .dxrd-localization-editor.dx-ltr .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field {
  padding-right: 0px;
}
.dx-designer .dxrd-localization-editor.dx-ltr .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dx-property-grid-search-box {
  right: 0px;
}
.dx-designer .dxrd-localization-editor.dx-ltr .dxrd-left-localization-panel .dxrd-localization-panel-content .dxrd-localization-panel-content-header {
  padding-right: 10px;
  padding-left: 16px;
}
.dx-designer .dxrd-localization-editor.dx-ltr .dxrd-left-localization-panel .dxrd-localization-panel-content .dxrd-localization-panel-content-header .dx-field-label {
  padding-left: 8px;
  padding-top: 0;
}
.dx-designer .dxrd-localization-editor.dx-ltr .dx-list-item-content {
  padding-left: 24px;
  padding-right: 30px;
}
.dx-designer .dxrd-localization-editor.dx-rtl {
  left: auto;
  right: 50px;
}
.dx-designer .dxrd-localization-editor.dx-rtl .dxrd-left-localization-panel .dxrd-localization-panel-header {
  height: 120px;
}
.dx-designer .dxrd-localization-editor.dx-rtl .dxrd-left-localization-panel .dxrd-localization-panel-header .dxrd-localization-panel-title {
  padding-right: 30px;
}
.dx-designer .dxrd-localization-editor.dx-rtl .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset {
  padding: 20px 16px 22px 30px;
}
.dx-designer .dxrd-localization-editor.dx-rtl .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field {
  padding-left: 0px;
}
.dx-designer .dxrd-localization-editor.dx-rtl .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dx-property-grid-search-box {
  left: 0px;
}
.dx-designer .dxrd-localization-editor.dx-rtl .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dx-field-value {
  right: 97px;
  left: 36px;
}
.dx-designer .dxrd-localization-editor.dx-rtl .dxrd-left-localization-panel .dxrd-localization-panel-header .dxrd-localization-panel-button {
  float: left;
}
.dx-designer .dxrd-localization-editor.dx-rtl .dxrd-left-localization-panel .dxrd-localization-panel-content .dxrd-localization-panel-content-header {
  padding-right: 16px;
  padding-left: 10px;
}
.dx-designer .dxrd-localization-editor.dx-rtl .dx-list-item-content {
  padding-left: 30px;
  padding-right: 24px;
}
.dx-designer .dxrd-localization-editor .dx-field {
  padding-left: 0px;
  padding-right: 0px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel {
  height: 100%;
  width: 100%;
  position: absolute;
  overflow: hidden;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dxrd-localization-panel-title {
  font-weight: bold;
  font-size: 13px;
  text-transform: uppercase;
  padding-top: 30px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dx-field-value {
  position: absolute;
  left: 97px;
  right: 36px;
  width: auto;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dx-field-value-shortened {
  right: 72px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dxrd-localization-panel-button {
  float: right;
  width: 26px;
  height: 26px;
  margin-left: 10px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dxrd-localization-language {
  width: calc(100% - 36px);
  position: relative;
  display: inline-block;
  -moz-transition: width 0.25s;
  -o-transition: width 0.25s;
  -webkit-transition: width 0.25s;
  transition: width 0.25s;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dxrd-localization-language.dx-property-grid-search-uncollapsed {
  width: calc(50% - 5px);
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dxrd-localization-language.dx-property-grid-search-uncollapsed .dx-field-label {
  visibility: hidden;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dxrd-localization-language.dx-property-grid-search-uncollapsed .dx-field-value {
  width: calc(100% - 36px);
  left: 0px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dx-property-grid-search-group {
  width: calc(50% - 5px);
  float: right;
  position: relative;
  top: -2px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dx-property-grid-search-group.dx-property-grid-search-collapsed {
  width: 36px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dx-property-grid-search-group .dx-property-grid-sorting-action {
  width: 26px;
  height: 26px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-header .dx-fieldset .dx-field .dx-icon-dxrd {
  margin: 0;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content {
  height: calc(100% - 135px);
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dxrd-localization-panel-content-header {
  display: table;
  padding: 30px 10px 15px 10px;
  width: calc(100% - 20px);
  table-layout: fixed;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dxrd-localization-panel-content-header .dx-fieldset {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: table-cell;
  width: 100%;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dxrd-localization-panel-content-header .dx-fieldset .dx-field {
  min-height: inherit;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dxrd-localization-panel-content-header .dx-fieldset .dx-field .dxrd-localization-language {
  width: calc(50% - 15px);
  display: inline-block;
  font-weight: 700;
  text-transform: uppercase;
  opacity: 0.5;
  font-size: 11px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dx-empty-message {
  text-align: center;
  padding: 20px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dxrd-localization-panel-default:not(.dxrd-localization-panel-textbox-full) {
  margin-right: 15px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dxrd-localization-panel-current {
  margin-left: 15px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dx-field-value:not(.dx-checkbox):not(.dx-button) {
  width: 70%;
  font-size: 0;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dx-field-value:not(.dx-checkbox):not(.dx-button) .dx-selectbox {
  width: 100%;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dx-field-label {
  max-width: 250px;
  padding-bottom: 0;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dx-list .dx-list-item {
  border-top: 0px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dx-list .dx-list-item .dx-list-item-content {
  padding-top: 5px;
  padding-bottom: 5px;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dx-list .dx-field-value .dx-texteditor {
  width: calc(50% - 15px);
  display: inline-block;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dx-list .dx-field-value .dx-texteditor.dxrd-localization-panel-textbox-full {
  width: 100%;
}
.dx-designer .dxrd-localization-editor .dxrd-left-localization-panel .dxrd-localization-panel-content .dx-field {
  padding-top: 2px;
  padding-bottom: 2px;
}
.dx-designer .dxrd-localization-editor .dxrd-multi-area-container {
  display: -webkit-flex;
  display: flex;
  max-height: 72px;
}
.dx-designer .dx-localization-combobox-content .dx-culture-code {
  width: 75px;
  opacity: 0.6;
}
.dx-designer .dx-localization-combobox-content .dx-culture {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: bottom;
  text-overflow: ellipsis;
  max-width: calc(100% - 80px);
}
.dx-designer .dx-rtl .dxrd-error-panel .dxrd-error-panel-header .dxrd-error-panel-header-collapse {
  float: left;
}
.dx-designer .dx-rtl .dxrd-error-panel .dxrd-error-panel-header .dxrd-error-panel-header-title {
  float: right;
}
.dx-designer .dxrd-error-panel {
  height: 210px;
  position: absolute;
  z-index: 100;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  bottom: 20px;
}
.dx-designer .dxrd-error-panel .dx-widget {
  font-size: 12px;
}
.dx-designer .dxrd-error-panel.collapsed {
  height: 42px;
  border-radius: 5px;
  width: 42px;
}
.dx-designer .dxrd-error-panel:not(.collapsed) {
  min-width: 350px;
}
.dx-designer .dxrd-error-panel .dxrd-svg-errorPanel-collectErrors svg {
  width: 16px;
  height: 16px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-notification {
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 9px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-header {
  height: 13px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  padding: 10px;
  font-size: 13px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-header .dxrd-error-panel-header-title {
  font-weight: bold;
  float: left;
  margin-left: 6px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-header .dxrd-error-panel-header-collapse {
  float: right;
  width: 16px;
  height: 16px;
  padding: 0px;
  font-size: 16px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-header .dxrd-error-panel-header-collapse::before {
  color: inherit;
  font-size: 16px;
  line-height: normal;
  margin-left: 0px;
  margin-top: 0px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content {
  height: calc(100% - 34px);
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dxrd-error-grid {
  height: 100%;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dxrd-errorType-column {
  width: 24px;
  height: 24px;
  vertical-align: top;
  display: inline-block;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dxrd-errorType-text {
  height: 24px;
  line-height: 24px;
  display: inline-block;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-datagrid-borders .dx-datagrid-rowsview,
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-datagrid-headers + .dx-datagrid-rowsview {
  border-top: 0px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-datagrid-headers {
  font-weight: bold;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-header-filter {
  font-size: 12px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-datagrid-rowsview .dx-row > .dx-master-detail-cell {
  padding: 7px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-datagrid-rowsview .dx-row > .dx-master-detail-cell .dxrd-error-panel-description {
  padding-left: 60px;
  white-space: normal;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-datagrid .dx-tag .dx-tag-content {
  margin: 2px 0px 0px 2px;
  padding-bottom: 3px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-datagrid .dx-row > td {
  padding: 4px 5px 4px 5px;
  vertical-align: middle;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-toolbar {
  padding: 12px;
  margin: auto;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-toolbar .dx-toolbar-items-container {
  height: 30px;
  overflow: hidden;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-toolbar .dx-selectbox,
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-toolbar .dx-datagrid-search-panel {
  height: 28px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-toolbar .dx-button {
  height: 30px;
  font-size: 12px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-toolbar .dxrd-collect-errors-button {
  padding-left: 15px;
}
.dx-designer .dxrd-error-panel .dxrd-error-panel-content .dx-toolbar .dx-buttongroup-item {
  border-left-width: 0px;
  border-bottom-width: 0px;
  border-top-width: 0px;
}
.dxrd-image-leaf-node {
  padding: 4.5px;
}
.dxrd-image-ghost-formattingrule,
.dxrd-image-ghost-stylemodel {
  border-width: 0px;
  border-style: none;
  border-width: 0;
  position: absolute;
}
.dx-designer .dxrd-designer-wrapper .dxrd-control-border-box {
  width: 100%;
  height: 100%;
  position: absolute;
  box-sizing: border-box;
}
.dxrd-designer-wrapper .dx-field-value .dx-font-style-button.dxd-border-accented {
  border-style: solid;
  border-width: 1px;
}
.dxrd-designer-wrapper .dx-field-value.dxd-border-accented .dx-texteditor,
.dxrd-designer-wrapper .dx-editor-content .dx-field.dxd-border-accented .dx-texteditor,
.dxrd-designer-wrapper .dx-field-value.dxd-border-accented .dx-checkbox,
.dxrd-designer-wrapper .dx-editor-content .dx-field.dxd-border-accented .dx-checkbox {
  border-style: solid;
  border-width: 1px;
  border-color: inherit;
}
.dxrd-designer-wrapper .dxrd-image-ghost-bounded {
  border-width: 0px;
  border-style: none;
  border-width: 0;
  position: absolute;
}
.dxrd-designer-wrapper .dxrd-expressions-tab .dx-property-grid-header .dx-property-grid-header-content .dx-property-grid-selected-component {
  padding: 2px 0;
}
.dxrd-designer-wrapper .dxrd-expressions-tab .dx-property-grid-header .dx-property-grid-header-content .dx-property-grid-selected-component .dxrd-properties-focused-item {
  height: 28px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper {
  position: absolute;
  top: 124px;
  left: 128px;
  right: 375px;
  bottom: 0;
  text-align: center;
  min-width: 100px;
}
.dx-rtl.dxrd-designer-wrapper .dxrd-surface-wrapper {
  left: 375px;
  right: 128px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-margin-resizable {
  position: absolute;
  z-index: 0;
  height: 100%;
  width: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-margin-resizable .ui-resizable-w,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-margin-resizable .ui-resizable-e {
  width: 5px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface {
  height: 100%;
  display: inline-block;
  position: relative;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-control-smart-tags {
  top: 0px;
  position: absolute;
  z-index: 1;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-control-smart-tags .dxrd-control-smart-tag {
  margin-bottom: 5px;
  border-radius: 0;
  left: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 20px;
  overflow: hidden;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel {
  width: 100%;
  height: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers {
  float: left;
  position: relative;
  width: 100%;
  height: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper {
  overflow: hidden;
  background: #fff;
  right: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-vruler {
  position: relative;
  width: 20px;
  float: right;
  overflow: hidden;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-vruler svg {
  position: absolute;
  top: 0;
  left: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker {
  width: 60px;
  right: 20px;
  position: absolute;
  overflow: hidden;
  box-sizing: border-box;
  z-index: 1;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker .dxrd-band-marker-content {
  border: 1px solid #a7a7ab;
  width: 100%;
  height: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker .dxrd-band-marker-rotation {
  height: 100%;
  -webkit-transform: rotate(270deg);
  -moz-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  -o-transform: rotate(270deg);
  -webkit-transform-origin: 0% 100%;
  -moz-transform-origin: 0% 100%;
  -ms-transform-origin: 0% 100%;
  -o-transform-origin: 0% 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker .dxrd-band-marker-rotation .dxrd-band-marker-rotation-text {
  position: absolute;
  top: 100%;
  padding-left: 1em;
  white-space: nowrap;
  padding-top: 0.5em;
  opacity: 0.75;
  cursor: default;
  color: #000000;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker.dxrd-band-marker-header {
  background-color: #B4E1DB;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker.dxrd-band-marker-header:hover {
  background-color: #C9F0EB;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker.dxrd-band-marker-header-focused {
  background: #93D3CA;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker.dxrd-band-marker-body {
  background-color: #B3DCEE;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker.dxrd-band-marker-body:hover {
  background-color: #C7ECF9;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker.dxrd-band-marker-body-focused {
  background: #92CBE6;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker.dxrd-band-marker-footer {
  background-color: #C5CCE7;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker.dxrd-band-marker-footer:hover {
  background-color: #D7DDF3;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker.dxrd-band-marker-footer-focused {
  background: #AAB4DB;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-hscroller-space {
  height: 20px;
  width: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper {
  right: 0;
  height: auto;
  top: 0;
  bottom: 20px;
  position: absolute;
  float: left;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-content-panel {
  height: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-hruler-wrapper {
  overflow: hidden;
  top: -20px;
  height: 20px;
  position: relative;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-hruler-wrapper .dxrd-hruler {
  position: absolute;
  height: 20px;
  background: yellow;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-hruler-wrapper .dxrd-ruler-shadow {
  position: absolute;
  top: 0;
  background: gray;
  opacity: 0.5;
  height: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-hruler-wrapper .dxrd-vscroller-space {
  width: 20px;
  height: 1px;
  position: absolute;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-hruler-wrapper .dxrd-margin-resizable .ui-resizable-w,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-hruler-wrapper .dxrd-margin-resizable .ui-resizable-e {
  width: 10px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport {
  height: inherit;
  width: inherit;
  top: -20px;
  overflow: auto;
  position: relative;
  background: white;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-leftMargin-resizable {
  position: absolute;
  z-index: 0;
  height: 100%;
  width: 100%;
  left: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-leftMargin-resizable .ui-resizable-w,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-leftMargin-resizable .ui-resizable-e {
  width: 5px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-leftMargin {
  position: absolute;
  z-index: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-leftMargin .dxrd-flip {
  -moz-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  -ms-transform: scaleX(-1);
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-leftMargin .dxrd-flip .dxrd-band-content {
  position: absolute;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-ghost-containment {
  position: absolute;
  height: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-ghost-containment .dxrd-ghost-container {
  position: absolute;
  height: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-ghost-containment .dxrd-ghost-container .dxrd-bands-content.dxrd-selection {
  height: 100%;
  position: absolute;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-ghost-containment .dxrd-ghost-container .dxrd-bands-content.dxrd-selection .dxrd-bands-content-offset {
  position: relative;
  height: 100%;
  width: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-ghost-containment .dxrd-ghost-container .dxrd-rightMargin-resizable {
  position: absolute;
  z-index: 0;
  height: 100%;
  width: 100%;
  width: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-ghost-containment .dxrd-ghost-container .dxrd-rightMargin-resizable .ui-resizable-w,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-ghost-containment .dxrd-ghost-container .dxrd-rightMargin-resizable .ui-resizable-e {
  width: 5px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-ghost-containment .dxrd-ghost-container .dxrd-rightMargin-border {
  height: 100%;
  position: absolute;
  top: 0;
  width: 1px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-ghost-containment .dxrd-ghost-container .dxrd-snap-line-holder-container {
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-collapsed {
  position: absolute;
  background: #EEEEEF;
  width: 100%;
  z-index: 20;
  border-bottom: 1px solid #B6B6BA;
  font-family: 'Helvetica Neue', 'Segoe UI', Helvetica, Verdana, san-serif;
  font-size: 10px;
  font-weight: bold;
  color: #747474;
  box-sizing: border-box;
  cursor: default;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: visible;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content svg {
  position: absolute;
  top: 0;
  left: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content .dxrd-band-content-grid {
  position: absolute;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content .dxrd-band-content-spasing {
  background-color: #FFF;
  height: 100%;
  position: absolute;
  overflow: hidden;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content .dxrd-band-content-greyarea {
  background-color: #CCC;
  position: absolute;
  height: 100%;
  overflow: hidden;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content .dxrd-band-content-watermark {
  position: absolute;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content.dxrd-vertical-bands-container {
  position: absolute;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content.dxrd-vertical-bands-container .dxrd-band-content-greyarea {
  display: inline-block;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content.dxrd-vertical-band {
  width: auto;
  display: inline-block;
  position: absolute;
  top: 29px;
  bottom: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content::before {
  content: '';
  position: absolute;
  width: 100%;
  left: 0;
  height: 1px;
  top: 0;
  border-top: 1px dashed #a7a7ab;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  left: 0;
  bottom: 0;
  border-bottom: 1px dashed #a7a7ab;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content-selected::before {
  border-top-width: 1px;
  border-top-style: solid;
  border-color: inherit;
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  top: -1px;
  left: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-content-selected::after {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-color: inherit;
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: 0;
  left: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-subbands-wrapper {
  width: 100%;
  position: absolute;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-band-subbands-wrapper::before {
  content: '';
  width: 100%;
  position: absolute;
  top: -1px;
  left: 0;
  border-top: 1px dashed #a7a7ab;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-control {
  position: absolute;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-control .dxrd-table-row-control {
  position: absolute;
  width: 100%;
  overflow: visible;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-control .dxrd-table-row-control .dxrd-table-cell-control {
  position: absolute;
  height: 100%;
  line-height: 100%;
  box-sizing: border-box;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-control .dxrd-table-row-control .dxrd-table-cell-control.dxrd-table-cell-over-rowSpan .ui-resizable-handle {
  height: 0;
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-smart-tags {
  z-index: 5;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control {
  position: absolute;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  direction: ltr;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control .dxrd-chart-panel {
  position: absolute;
  right: 2px;
  left: 2px;
  top: 2px;
  height: 25px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control .dxrd-chart-panel .dxrd-chart-panel-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  background-color: #2a2a2a;
  color: #fff;
  line-height: normal;
  padding: 5px 8px 5px 8px;
  font-weight: bold;
  border-radius: 3px;
  cursor: pointer;
  font-family: 'Helvetica Neue', 'Segoe UI', Helvetica, Verdana, san-serif;
  height: 25px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  max-width: 100%;
  float: right;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control .dxrd-chart-panel-text:hover {
  background-color: #444444;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control.dxrd-control-rtl .dxrd-transform-origin-left-top {
  direction: rtl;
  unicode-bidi: embed;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .inline-text-edit {
  -position: absolute;
  width: 100%;
  height: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .inline-text-edit .dx-texteditor-input {
  background-color: white;
  color: black;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main {
  letter-spacing: -2px;
  position: absolute;
  display: inline-block;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-position-absolute img {
  position: inherit;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-checkbox-checkstate {
  display: inline-block;
  line-height: normal;
  vertical-align: middle;
  position: relative;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-checkbox-checkstate svg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-checkbox-checkstate .dxrd-checkbox-custom-glyph {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-pdf-signature-skeleton {
  box-sizing: border-box;
  letter-spacing: normal;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-pdf-signature-image {
  position: absolute;
  width: 25%;
  height: 24px;
  left: 15%;
  top: 25%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-pdf-signature-image-fill-container {
  left: 40%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-pdf-signature-info {
  display: inline-block;
  box-sizing: border-box;
  letter-spacing: normal;
  padding-left: 3px;
  position: absolute;
  right: 5px;
  width: 50%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-pdf-signature-info .dxrd-control-content {
  text-align: left;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-pdf-signature-info-fill-container {
  width: 100%;
  left: 5px;
  right: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-pdf-signature-info-certificate-name {
  position: absolute;
  left: 5px;
  width: 50%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-pdf-signature-info-certificate-name .dxrd-control-content {
  text-align: left;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-main .dxrd-pdf-signature-info-certificate-name-fill-container {
  width: 100%;
  right: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control-content-select-main {
  position: absolute;
  display: inline-block;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-cross-band {
  position: absolute;
  z-index: 1;
  box-sizing: border-box;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-cross-band.dxrd-intersect {
  box-sizing: content-box;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection {
  z-index: 5;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected.dxrd-pagebreak .dxrd-control-border-box,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused.dxrd-pagebreak .dxrd-control-border-box {
  border-width: 1px;
  border-style: solid;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected .dxrd-control-border-box,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused .dxrd-control-border-box {
  border-width: 2px;
  border-style: solid;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-pagebreak.dxrd-selected.dxrd-locked {
  border: 2px solid orangered;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-of-contents-selected {
  z-index: 10;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused .ui-resizable-handle,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected .ui-resizable-handle,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-of-contents-selected .ui-resizable-handle {
  position: absolute;
  width: 5px;
  height: 5px;
  font-size: 1px;
  border: 1px solid #333;
  visibility: inherit;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused .ui-resizable-nw,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected .ui-resizable-nw,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-of-contents-selected .ui-resizable-nw {
  top: -8px;
  left: -8px;
  cursor: nw-resize;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused .ui-resizable-n,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected .ui-resizable-n,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-of-contents-selected .ui-resizable-n {
  top: -8px;
  left: 50%;
  margin-left: -4px;
  cursor: n-resize;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused .ui-resizable-ne,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected .ui-resizable-ne,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-of-contents-selected .ui-resizable-ne {
  top: -8px;
  right: -8px;
  cursor: ne-resize;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused .ui-resizable-w,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected .ui-resizable-w,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-of-contents-selected .ui-resizable-w {
  top: 50%;
  margin-top: -4px;
  left: -8px;
  cursor: w-resize;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused .ui-resizable-e,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected .ui-resizable-e,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-of-contents-selected .ui-resizable-e {
  top: 50%;
  margin-top: -4px;
  right: -8px;
  cursor: e-resize;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused .ui-resizable-sw,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected .ui-resizable-sw,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-of-contents-selected .ui-resizable-sw {
  bottom: -8px;
  left: -8px;
  cursor: sw-resize;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused .ui-resizable-s,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected .ui-resizable-s,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-of-contents-selected .ui-resizable-s {
  bottom: -8px;
  left: 50%;
  margin-left: -4px;
  cursor: s-resize;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-focused .ui-resizable-se,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected .ui-resizable-se,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-of-contents-selected .ui-resizable-se {
  bottom: -8px;
  right: -8px;
  cursor: se-resize;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-richedit-selected {
  visibility: visible;
  overflow: hidden;
  z-index: 11;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-richedit-selected .dxrd-control-content-main {
  letter-spacing: normal;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-richedit-selected-content {
  position: relative;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-richedit-selected-content .dxreControl {
  border-width: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-locked {
  border: 1px solid orangered;
  box-sizing: border-box;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected:not(.dxrd-focused):not(.dxrd-intersect) > .ui-resizable-handle {
  background: black;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-intersect {
  border: 1px solid #e04a6f;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-intersect > .ui-resizable-handle {
  border: 1px solid #e04a6f;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-intersect:not(.dxrd-selected) > .ui-resizable-handle {
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-selected.dxrd-intersect:not(.dxrd-focused) > .ui-resizable-handle {
  background: #e04a6f;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control {
  overflow: visible;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-row-control {
  overflow: visible;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-row-control .dxrd-focused .ui-resizable-handle {
  visibility: inherit;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-row-control .dxrd-table-cell-control.dxrd-focused,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-row-control .dxrd-selected {
  overflow: visible;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-row-control .dxrd-table-cell-control.dxrd-focused .ui-resizable-handle,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-row-control .dxrd-selected .ui-resizable-handle {
  visibility: inherit;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-row-control .dxrd-table-row-selection,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-crosstab-cell .dxrd-table-row-selection {
  height: 100%;
  position: absolute;
  top: -1px;
  width: 10px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-row-control .dxrd-table-row-selection-left,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-crosstab-cell .dxrd-table-row-selection-left {
  left: -6px;
  cursor: url(data:application/octet-stream;base64,AAACAAEAICAQABQADwDoAgAAFgAAACgAAAAgAAAAQAAAAAEABAAAAAAAgAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAwMDAAICAgAAAAP8AAP8AAAD//wD/AAAA/wD/AP//AAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////////////////////////////////+/////n///wA///8AH///AD////5////+//////////////////////////////////////////////////////////////////w==), pointer;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-row-control .dxrd-table-row-selection-right,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-crosstab-cell .dxrd-table-row-selection-right {
  right: -6px;
  cursor: url(data:application/octet-stream;base64,AAACAAEAICAQAAoADwDoAgAAFgAAACgAAAAgAAAAQAAAAAEABAAAAAAAgAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAwMDAAICAgAAAAP8AAP8AAAD//wD/AAAA/wD/AP//AAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////////////////////////////////v////z////4Af//8AH///gB///8/////v//////////////////////////////////////////////////////////////////w==), pointer;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-column-selection,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-crosstab-cell .dxrd-table-column-selection {
  margin-left: -1px;
  height: 10px;
  position: absolute;
  /*border: solid 1px blue;*/
  z-index: 1;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-column-selection-top,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-crosstab-cell .dxrd-table-column-selection-top {
  top: -6px;
  cursor: url(data:application/octet-stream;base64,AAACAAEAICAQAA8AFQDoAgAAFgAAACgAAAAgAAAAQAAAAAEABAAAAAAAgAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAwMDAAICAgAAAAP8AAP8AAAD//wD/AAAA/wD/AP//AAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///////////////////////////////////////////////////////7////8f///+D////Af///8f////H////x////8f////H////x////8f////////////////////////////////////////////////////////////w==), pointer;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-table-control .dxrd-table-column-selection-bottom,
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-crosstab-cell .dxrd-table-column-selection-bottom {
  bottom: -6px;
  cursor: url(data:application/octet-stream;base64,AAACAAEAICAQAA8ACgDoAgAAFgAAACgAAAAgAAAAQAAAAAEABAAAAAAAgAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAwMDAAICAgAAAAP8AAP8AAAD//wD/AAAA/wD/AP//AAD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD////////////////////////////////////////////////////////////8f////H////x////8f////H////x////8f///8B////g////8f////v///////////////////////////////////////////////////////w==), pointer;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-vertical-band-marker {
  height: 27px;
  border: 1px solid #a7a7ab;
  position: relative;
  overflow: hidden;
  background-color: #B3DCEE;
  text-align: left;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-vertical-band-marker .dxrd-banding-marker-text {
  position: absolute;
  padding-left: 1em;
  padding-top: 0.5em;
  opacity: 0.75;
  cursor: default;
  color: #000000;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-vertical-band-marker:hover {
  background-color: #C7ECF9;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection .dxrd-vertical-band-marker.dxrd-vertical-band-marker-focused {
  background-color: #92CBE6;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main {
  overflow: hidden;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control {
  background-color: transparent;
  overflow: hidden;
  display: inline-block;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content::before {
  content: '';
  position: absolute;
  border: 1px solid silver;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content.dxrd-intersect {
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content.dxrd-intersect::before {
  border: 1px solid rgba(255, 0, 0, 0.5);
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content .dxrd-control-table-of-content-levelsElements .dxrd-control-content .dxrd-control-content-level-text {
  overflow: hidden;
  height: 100%;
  line-height: inherit;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content .dxrd-control-table-of-content-levelsElements .dxrd-control-content .dxrd-control-content-level-text table {
  border-width: 0px;
  border-style: none;
  vertical-align: top;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content .dxrd-control-table-of-content-levelsElements .dxrd-control-content .dxrd-control-content-level-text table td {
  vertical-align: top;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content .dxrd-control-table-of-content-levelsElements .dxrd-control-content .dxrd-control-content-level-text .dxrd-control-content-line-text {
  white-space: nowrap;
  padding-right: 7px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content .dxrd-control-table-of-content-levelsElements .dxrd-control-content .dxrd-control-content-level-text .dxrd-control-content-number {
  white-space: nowrap;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content .dxrd-control .dxrd-tocLevel-border {
  position: absolute;
  z-index: 1;
  bottom: 0;
  background-color: silver;
  left: 0;
  right: 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content .dxrd-control:last-child .dxrd-tocLevel-border {
  display: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content.dxrd-control-rtl .dxrd-control .dxrd-control-content.dxrd-control-content-multiline {
  display: block;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content.dxrd-control-rtl .dxrd-control-content.dxrd-control-content-multiline::after {
  content: "\200E‎";
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content.dxrd-control-rtl .dxrd-control-table-of-content-levelsElements .dxrd-control-content .dxrd-control-content-level-text .dxrd-control-content-line-text {
  padding-left: 7px;
  padding-right: 0px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control.dxrd-control-table-of-content.dxrd-control-rtl .dxrd-control-content-line-text::after {
  content: "\200E‎";
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-intersect {
  border: 1px solid rgba(255, 0, 0, 0.5);
  background-color: rgba(255, 0, 0, 0.1);
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control .dxrd-control-content {
  display: inline-block;
  position: relative;
  overflow: hidden;
  line-height: normal;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  letter-spacing: normal;
  cursor: default;
  overflow-wrap: break-word;
  width: 100%;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-control-content-multiline {
  word-wrap: break-word;
  white-space: pre-wrap;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-charactercomb-cell {
  display: inline-block;
  vertical-align: top;
  float: left;
  overflow: hidden;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-cross-band {
  background-color: transparent;
  overflow: hidden;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-cross-band .dxrd-control {
  background-color: transparent;
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-cross-band .dxrd-cross-band-intersect {
  stroke: rgba(255, 0, 0, 0.2);
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-main .dxrd-print-across-bands-placeholder {
  position: absolute;
  opacity: 0.5;
  pointer-events: none;
  overflow: hidden;
  width: 100%;
  z-index: 1;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-of-contents-selected .dxrd-control-border-box {
  border-width: 2px;
  border-style: solid;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-of-contents-selected .ui-resizable-handle {
  bottom: -6px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-of-contents-selected.dxrd-intersect .ui-resizable-handle {
  border: 1px solid #e04a6f;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-of-contents-selected.dxrd-intersect .ui-resizable-resizing .ui-resizable-handle {
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-of-contents-selected.dxrd-intersect .dxrd-control-line-ui-resizeble .ui-resizable-handle {
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-of-contents-selected .dxrd-control-line-ui-resizeble .ui-resizable-s {
  width: 100%;
  height: 7px;
  bottom: -5px;
  left: 4px;
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-table-of-contents-selected .ui-resizable-resizing .ui-resizable-s {
  width: 100%;
  height: 7px;
  bottom: -5px;
  left: 4px;
  border-width: 0px;
  border-style: none;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAIAAAAW4yFwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAMSURBVBhXYwADBgYAAAgAAWnvujMAAAAASUVORK5CYII=) repeat-x 3px 0;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection.ghost .dxrd-focused {
  border: black 2px dotted;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection.ghost .dxrd-selected {
  border: black 2px dotted;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-selection.ghost .ui-resizable-handle {
  display: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-surface-hidden::after {
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  position: absolute;
  content: " ";
  z-index: 1;
  background: repeating-linear-gradient(-45deg, rgba(85, 85, 85, 0.25) 0, rgba(85, 85, 85, 0.25) 2px, transparent 1px, transparent 5px);
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-image-surface-base {
  background-repeat: no-repeat;
  background-size: 16px 16px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-image-surface-bounded:after {
  background-repeat: no-repeat;
  background-size: 16px 16px;
  background-position: right top;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  position: absolute;
  content: " ";
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-image-surface-bounded-notvalid:after {
  background-repeat: no-repeat;
  background-size: 16px 16px;
  background-position: right top;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  position: absolute;
  content: " ";
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-image-surface-bounded-warning:after {
  background-repeat: no-repeat;
  background-size: 16px 16px;
  background-position: right top;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  position: absolute;
  content: " ";
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-image-surface-picturebox_placeholder {
  background-repeat: no-repeat;
  background-size: 16px 16px;
  background-position: 1px 1px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-control-rtl.dxrd-image-surface-bounded:after {
  background-position: left top;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface .dxrd-control-rtl.dxrd-image-surface-picturebox_placeholder {
  background-position: right top;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface:not(.dxrd-surface-rtl) .dxrd-content-panel-wrapper .dxrd-viewport .dxrd-control.dxrd-control-rtl .dxrd-pivot-content {
  direction: ltr;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode {
  transition: width, left, right, 0.35s ease-in-out;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-band-content-grid {
  display: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-control:before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-table-row-control:before {
  content: "";
  position: absolute;
  z-index: 1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.4);
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-control:not(.dxrd-uiselected):before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-table-row-control:not(.dxrd-uiselected):before {
  display: block;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-control.dxrd-uiselected:before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-table-row-control.dxrd-uiselected:before {
  display: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-control:not(.dxrd-uiselected) .dxrd-control:before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-table-row-control:not(.dxrd-uiselected) .dxrd-control:before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-control:not(.dxrd-uiselected) .dxrd-table-row-control:before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-table-row-control:not(.dxrd-uiselected) .dxrd-table-row-control:before {
  display: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-uiselected {
  z-index: 2;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-uiselected .dxrd-control:not(.dxrd-uiselected):before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-uiselected .dxrd-table-row-control:not(.dxrd-uiselected):before {
  display: block;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-uiselected .dxrd-control.dxrd-uiselected:before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-uiselected .dxrd-table-row-control.dxrd-uiselected:before {
  display: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-uiselected .dxrd-control:not(.dxrd-uiselected) .dxrd-control:before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-uiselected .dxrd-table-row-control:not(.dxrd-uiselected) .dxrd-control:before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-uiselected .dxrd-control:not(.dxrd-uiselected) .dxrd-table-row-control:before,
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-bands-content.dxrd-main .dxrd-uiselected .dxrd-table-row-control:not(.dxrd-uiselected) .dxrd-table-row-control:before {
  display: none;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper.dxrd-surface-localization-mode .dxrd-band-marker:before {
  content: "";
  position: absolute;
  z-index: 1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.4);
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface:not(.dxrd-surface-rtl) .dxrd-bands-content.dxrd-selection {
  left: -8px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface:not(.dxrd-surface-rtl) .dxrd-bands-content.dxrd-selection .dxrd-bands-content-offset {
  left: 8px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface-rtl .dxrd-bands-content.dxrd-selection {
  right: -8px;
}
.dxrd-designer-wrapper .dxrd-surface-wrapper .dxrd-surface-rtl .dxrd-bands-content.dxrd-selection .dxrd-bands-content-offset {
  right: 8px;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper {
  bottom: 0;
  display: block;
  position: absolute;
  box-sizing: border-box;
  padding: 16px;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs-scrollable::after {
  border: none;
}
.dx-rtl.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper {
  right: 128px;
}
.dx-ltr.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper {
  left: 128px;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs {
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs.dx-widget {
  background-color: transparent;
  box-shadow: none;
  width: auto;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs.dx-widget .dx-tabs-wrapper .dx-item {
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper {
  background: transparent;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper::after,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper::after {
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab {
  max-width: fit-content;
  border-width: 1px;
  border-style: solid;
  margin: 4px;
  height: 42px;
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab .dx-tab-content,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab .dx-tab-content {
  vertical-align: middle;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab .dx-tab-content .dx-icon,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab .dx-tab-content .dx-icon {
  font-size: 14px;
}
.dx-rtl.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab .dx-tab-content .dx-icon,
.dx-rtl.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab .dx-tab-content .dx-icon {
  float: left;
  margin-left: 4px;
  margin-right: 11px;
}
.dx-ltr.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab .dx-tab-content .dx-icon,
.dx-ltr.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab .dx-tab-content .dx-icon {
  float: right;
  margin-left: 11px;
  margin-right: 4px;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper {
  overflow: hidden;
  max-width: 312px;
}
.dx-rtl.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper,
.dx-rtl.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper {
  float: right;
}
.dx-ltr.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper,
.dx-ltr.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper {
  float: left;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper .dx-tab-title,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper .dx-tab-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  width: 100%;
}
.dx-rtl.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper .dx-tab-title,
.dx-rtl.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper .dx-tab-title {
  padding-left: 12px;
}
.dx-ltr.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper .dx-tab-title,
.dx-ltr.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab .dx-tab-content .dx-tab-title-wrapper .dx-tab-title {
  padding-right: 12px;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab-selected,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab-selected {
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  cursor: default;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab-selected:after,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab-selected:after {
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-tab.dx-state-focused:after,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-tab.dx-state-focused:after {
  border-width: 0px;
  border-style: none;
}
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-tabs-wrapper .dx-state-focused,
.dxrd-designer-wrapper .dxrd-navigation-panel-wrapper .dx-tabs .dx-indent-wrapper .dx-state-focused {
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dx-overlay-wrapper {
  height: 100%;
  position: absolute;
  top: 0;
}
.dx-rtl.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dx-overlay-wrapper {
  right: 0;
}
.dx-ltr.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dx-overlay-wrapper {
  left: 0;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dx-overlay-wrapper .dx-overlay-content {
  width: 100%;
}
.dx-rtl.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dx-overlay-wrapper.dxrd-wizard.dxrd-report-wizard {
  left: 0;
  right: 50px;
}
.dx-ltr.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dx-overlay-wrapper.dxrd-wizard.dxrd-report-wizard {
  right: 0;
  left: 50px;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-report-menu-title {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  font-family: 'Helvetica Neue', 'Segoe UI', Helvetica, Verdana, san-serif;
  font-weight: 500;
  font-size: 13px;
  text-transform: uppercase;
  height: 65px;
  line-height: 65px;
  position: relative;
  width: 100%;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-report-menu-title .dxrd-report-menu-title-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.dx-rtl.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-report-menu-title .dxrd-report-menu-title-text {
  padding-right: 27px;
  padding-left: 52px;
}
.dx-ltr.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-report-menu-title .dxrd-report-menu-title-text {
  padding-left: 27px;
  padding-right: 52px;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-report-menu-title .dxrd-report-menu-button {
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-report-menu-title .dxrd-report-menu-button svg {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  pointer-events: none;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-report-menu-title .dxrd-report-menu-back-button {
  position: relative;
  width: 100%;
  height: 100%;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-report-menu-title .dxrd-report-menu-close-button {
  position: absolute;
  top: 16px;
}
.dx-rtl.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-report-menu-title .dxrd-report-menu-close-button {
  left: 6px;
}
.dx-ltr.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-report-menu-title .dxrd-report-menu-close-button {
  right: 13px;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-menu-item {
  margin-top: 0px;
  height: 48px;
  line-height: 48px;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-menu-item .dxrd-menu-item-image {
  height: 48px;
}
.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-menu-item .dxrd-menu-item-text {
  font-size: 13px;
  line-height: 48px;
}
.dx-rtl.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-menu-item .dxrd-menu-item-text {
  padding-right: 21px;
}
.dx-ltr.dxrd-designer-wrapper .dxrd-menubutton-part .dxrd-menu-container .dxrd-menu-item .dxrd-menu-item-text {
  padding-left: 21px;
}
.dxrd-designer-wrapper.dx-rtl .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper {
  right: 0;
  float: right;
  left: auto;
}
.dxrd-designer-wrapper.dx-rtl .dxrd-surface-wrapper .dxrd-surface .dxrd-content-panel-wrapper {
  float: right;
  left: 0;
}
.dxrd-designer-wrapper.dx-rtl .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper {
  left: 0;
}
.dxrd-designer-wrapper.dx-rtl .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-vruler {
  float: left;
}
.dxrd-designer-wrapper.dx-rtl .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker {
  left: 20px;
  right: auto;
}
.dxrd-designer-wrapper.dx-rtl .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker .dxrd-band-marker-rotation {
  margin-left: 100%;
}
.dxrd-designer-wrapper.dx-rtl .dxrd-surface-wrapper .dxrd-surface .dxrd-bands-panel-wrapper .dxrd-bands-panel .dxrd-band-headers .dxrd-band-marker-wrapper .dxrd-band-marker .dxrd-band-marker-rotation .dxrd-band-marker-rotation-text {
  bottom: 0;
  top: auto;
  padding-bottom: 0.5em;
  padding-top: inherit;
}
.dxrd-chartdesigner-popup .dx-popup-content {
  padding: 0;
}
.dxrd-chartdesigner-popup .dxrd-chartdesigner-popup-content {
  height: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.dxrd-add-dataSource-popup-content .dx-popup-content .dxrd-list-item-ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
}
.dx-dropdownlist-popup-wrapper .dxrd-checkbox-style-combobox-item .dxrd-checkbox-style-combobox-item-icons svg,
.dx-dropdownlist-popup-wrapper .dxrd-checkbox-style-combobox-item span {
  vertical-align: middle;
  display: inline-block;
}
.dx-dropdownlist-popup-wrapper .dxrd-checkbox-style-combobox-item-icons {
  width: 55px;
  display: inline-block;
}
.dx-dropdownlist-popup-wrapper .dxrd-checkbox-style-combobox-item-icons svg {
  width: 14px;
  height: 14px;
}
.dx-designer .dxrd-rich-surface:focus {
  outline: none;
}
.dx-designer .dxrd-rich-surface .dxreView,
.dx-designer .dxrd-rich-surface .dxrePage {
  background-color: transparent !important;
}
.dx-designer .dxrd-rich-surface.dxrd-richedit-readonly .dxreView {
  overflow: hidden;
}
.dx-designer .dxrd-rich-surface.dxrd-richedit-readonly .dxrePageArea {
  background-color: transparent !important;
}
.dx-designer .dxrd-rich-surface.dxrd-richedit-readonly .dxreInFocus .dxreSelCursor {
  display: none;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dxrd-toolbar-elements-line {
  max-width: 216px;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dxrd-toolbar-elements-line .dxrd-toolbar-elements-line-bottom {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  position: absolute;
  left: 0;
  right: 0;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dxrd-toolbar-elements-line .dxrd-rich-toolbar-header {
  padding: 7px 8px 1px;
  font-weight: bold;
  font-size: 11px;
  line-height: 11px;
  opacity: 0.5;
  text-align: left;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dxrd-toolbar-elements-line .dxrd-rich-toolbar-value {
  display: flex;
  padding: 2px;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dxrd-toolbar-elements-line .dxrd-rich-toolbar-value .dx-texteditor-input {
  font-size: 14px;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dxrd-toolbar-elements-line .dxrd-rich-toolbar-value .dxrd-richedit-toolbar-fontsize {
  width: 210px;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dxrd-toolbar-elements-line .dxrd-rich-toolbar-value .dxrd-richedit-toolbar-fontsize .dxrd-richedit-toolbar-fontsize-content {
  width: auto;
  display: inline-block;
  transition: transform 0.4s ease-in-out;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dxrd-toolbar-elements-line .dx-button-has-icon .dx-icon {
  width: 16px;
  height: 16px;
  font-size: 16px;
  line-height: 16px;
  background-size: 16px;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dxrd-toolbar-elements-line .dx-button-has-icon .dx-button-content {
  padding: 6px;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dx-button.dx-button-normal,
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dx-colorbox,
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dx-selectbox {
  margin: 6px;
  height: 30px;
  width: 30px;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dx-colorbox,
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dx-selectbox {
  width: 100%;
}
.dx-popup-wrapper > .dx-overlay-content .dxrd-rich-toolbar-popover-content .dx-button-has-text .dx-button-content {
  font-size: 14px;
  padding: 0;
}
.dx-content-loading-panel {
  position: absolute;
  width: auto;
  height: auto;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}
.dx-expressioneditor .dx-popup-content .dx-expression-variables-arguments {
  display: none;
}
.dx-expressioneditor .dx-popup-content.dx-expression-popup-related-xrcrosstabcell .dx-expression-variables-datasource {
  display: none;
}
.dx-expressioneditor .dx-popup-content.dx-expression-popup-related-xrcrosstabcell .dx-expression-variables-arguments {
  display: block;
}
.dx-expressioneditor .dx-popup-bottom.dx-toolbar {
  padding: 0px 9px 26px 30px;
}
.dx-expressioneditor.dxrd-expressioneditor-complex .dx-popup-bottom.dx-toolbar {
  padding: 15px 9px 26px 30px;
}
.dxrd-expressioneditor-popup-content {
  display: flex;
  height: 100%;
}
.dxrd-expressioneditor-popup-content .dx-expression-tree .dx-expressioneditor-tools .dx-expressioneditor-tools-tabs .dx-expressioneditor-tools-tab .dx-expressioneditor-tools-tab-category {
  height: 23px;
  line-height: 23px;
}
.dxrd-expressioneditor-popup-content .dx-expression-tree .dx-expressioneditor-tools .dx-expressioneditor-tools-tabs .dx-expressioneditor-tools-tab .dx-expressioneditor-tools-tab-item {
  height: 23px;
  line-height: 23px;
}
.dxrd-expressioneditor-popup-content .dx-expression-tree .dx-expressioneditor-tools .dx-expressioneditor-tools-tabs .dx-expressioneditor-tools-tab .dx-expressioneditor-tools-tab-header {
  height: 23px;
  line-height: 23px;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties {
  margin: 17px 0 0 17px;
  width: 200px;
  min-width: 120px;
  max-width: 70%;
  flex-shrink: 0;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper {
  border-width: 1px;
  border-style: solid;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dx-accordion-content {
  padding-left: 20px;
}
.dx-rtl .dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dx-accordion-content {
  padding-right: 20px;
  padding-left: 0;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dxrd-expressioneditor-property {
  height: 23px;
  line-height: 23px;
  padding-left: 5px;
  font-size: 11px;
}
.dx-rtl .dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dxrd-expressioneditor-property {
  padding-right: 5px;
  padding-left: 0;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dxrd-expressioneditor-property .dxrd-expressioneditor-property-text {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dxrd-expressioneditor-property .dxrd-expressioneditor-property-binded {
  font-weight: bold;
  padding-right: 24px;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dxrd-expressioneditor-property .dxrd-expression-image {
  display: inline-block;
  position: absolute;
  width: 12px;
  right: 10px;
  margin-top: 2px;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dx-expressioneditor-property-accordion-header {
  height: 23px;
  line-height: 23px;
  padding-left: 5px;
  font-size: 11px;
}
.dx-rtl .dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dx-expressioneditor-property-accordion-header {
  padding-right: 5px;
  padding-left: 0;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dx-expressioneditor-property-accordion-header .dxrd-expressioneditor-property-text {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dx-expressioneditor-property-accordion-header .dxrd-expressioneditor-property-binded {
  font-weight: bold;
  padding-right: 24px;
}
.dxrd-expressioneditor-popup-content .dxrd-expressioneditor-properties .dxrd-expressioneditor-properties-content-wrapper .dx-expressioneditor-property-accordion-header .dxrd-expression-image {
  display: inline-block;
  position: absolute;
  width: 12px;
  right: 10px;
  margin-top: 2px;
}
.dxrd-expressioneditor-popup-content .dx-expressioneditor-content {
  width: 100%;
}
.dxrd-designer-wrapper.dxrd-designer.dx-ltr .dxrd-properties-wrapper .dx-popup-general,
.dxrd-designer-wrapper.dxrd-designer.dx-ltr .dxrd-properties-wrapper .dx-datebox-container,
.dxrd-designer-wrapper.dxrd-designer.dx-ltr .dxrd-properties-wrapper .dx-widget:not(.dx-scrollable-scrollbar):not(.dx-dropdowneditor-button):not(.dx-button-has-icon):not(.no-margin-right) {
  margin-right: 37px;
}
.dxrd-designer-wrapper.dxrd-designer.dx-ltr .dxrd-properties-wrapper.dxrd-expressions-tab .dx-popup-general,
.dxrd-designer-wrapper.dxrd-designer.dx-ltr .dxrd-properties-wrapper.dxrd-expressions-tab .dx-datebox-container,
.dxrd-designer-wrapper.dxrd-designer.dx-ltr .dxrd-properties-wrapper.dxrd-expressions-tab .dx-widget:not(.dx-scrollable-scrollbar):not(.dx-dropdowneditor-button):not(.dx-button-has-icon):not(.no-margin-right) {
  margin-right: 0px;
}
.dxrd-designer-wrapper.dxrd-designer.dx-ltr .dxrd-properties-wrapper .dxrd-editormenu-boxes {
  right: 6px;
}
.dxrd-designer-wrapper.dxrd-designer.dx-rtl .dxrd-properties-wrapper .dx-popup-general,
.dxrd-designer-wrapper.dxrd-designer.dx-rtl .dxrd-properties-wrapper .dx-datebox-container,
.dxrd-designer-wrapper.dxrd-designer.dx-rtl .dxrd-properties-wrapper .dx-widget:not(.dx-scrollable-scrollbar):not(.dx-dropdowneditor-button):not(.dx-button-has-icon):not(.no-margin-right) {
  margin-left: 37px;
}
.dxrd-designer-wrapper.dxrd-designer.dx-rtl .dxrd-properties-wrapper.dxrd-expressions-tab .dx-popup-general,
.dxrd-designer-wrapper.dxrd-designer.dx-rtl .dxrd-properties-wrapper.dxrd-expressions-tab .dx-datebox-container,
.dxrd-designer-wrapper.dxrd-designer.dx-rtl .dxrd-properties-wrapper.dxrd-expressions-tab .dx-widget:not(.dx-scrollable-scrollbar):not(.dx-dropdowneditor-button):not(.dx-button-has-icon):not(.no-margin-right) {
  margin-left: 0px;
}
.dxrd-designer-wrapper.dxrd-designer.dx-rtl .dxrd-properties-wrapper .dxrd-editormenu-boxes {
  left: 6px;
}
.dxrd-designer-wrapper.dxrd-designer .dxrd-internal-error {
  color: #d9534f;
  font-size: 12px;
  font-family: "Helvetica Neue", "Segoe UI", helvetica, verdana, sans-serif;
  letter-spacing: initial;
  line-height: normal;
  text-align: left;
  margin-left: 1em;
  margin-right: 1em;
}
.dx-context-menu.dx-context-menu-flex {
  display: flex;
}
.dx-context-menu {
  align-items: center;
  width: 100%;
}
.dx-context-menu .dx-context-menu-icon {
  width: 14px;
  height: 14px;
  margin-left: 8px;
  margin-right: 8px;
  display: inline-block;
}
.dx-context-menu .dx-context-menu-text {
  display: inline-flex;
  margin-left: 8px;
  margin-right: 8px;
}
.dx-context-menu .dx-context-menu-group-icon {
  width: 14px;
  height: 14px;
  display: inline-flex;
  float: right;
  margin-top: 3px;
}
.dx-designer-viewport .dxrd-fieldslist-wrapper #btn-add-dataSource {
  line-height: 24px;
  margin-top: 8px;
  float: right;
  max-width: 166px;
  height: 24px;
  font-size: 12px;
  padding: 0px 9px 0px 9px;
}
.dx-designer-viewport .dxrd-fieldslist-wrapper #btn-add-dataSource .dx-button-text {
  text-transform: none;
}
.dx-designer-viewport .dxrd-fieldslist-wrapper .dx-treelist-item.dx-treelist-item-disabled {
  opacity: 0.5;
  pointer-events: none;
}
.dx-designer-viewport .dxrd-fieldslist-wrapper .dx-treelist-item .dx-treelist-rename-node {
  margin-top: 4px;
  position: absolute;
  right: 16px;
  left: 38px;
  height: 22px;
}
.dx-designer-viewport .dxrd-add-dataSource-popup-content .dx-create-dataSource-popup-item {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  margin-bottom: 3px;
}
.dx-designer-viewport .dxrd-add-dataSource-popup-content .dx-list {
  padding: 4px 0px;
}
.dx-designer-viewport .dxrd-add-dataSource-popup-content .dx-list .dx-list-search {
  margin: 3px;
}
.dx-designer-viewport .dxrd-add-dataSource-popup-content .dx-list .dx-item-content.dx-list-item-content {
  padding: 0px;
}
.dx-designer-viewport .dxrd-add-dataSource-popup-content .dx-list .dx-item-content.dx-list-item-content::before {
  content: '';
}
.dx-designer-viewport .dxrd-add-dataSource-popup-content .dx-list .dx-item-content.dx-list-item-content .dxrd-list-item-ellipsis-text {
  padding: 10px;
}
