<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{0ee4254d-27ec-4764-8c2f-b2bb47178bef}</ProjectGuid>
        <UserSecretsId>8aeb8a75-e753-4d5d-94c8-04f21a4c26fd</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
        <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.4.2"/>
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.8" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="9.0.8" />
        <PackageReference Include="Microsoft.Extensions.ApiDescription.Server" Version="9.0.8">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="9.8.0" />
        <PackageReference Include="Microsoft.Extensions.ServiceDiscovery" Version="9.4.2"/>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
        <PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.7.2" />
        <PackageReference Include="Scrutor" Version="6.1.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
        <PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
        <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
        <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
        <PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.4" />
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Bootis.Shared.Infrastructure\Bootis.Shared.Infrastructure.csproj" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="Resources\bootis-logo.svg" />
        <EmbeddedResource Include="Resources\swagger-dark.css" />
    </ItemGroup>

</Project>

