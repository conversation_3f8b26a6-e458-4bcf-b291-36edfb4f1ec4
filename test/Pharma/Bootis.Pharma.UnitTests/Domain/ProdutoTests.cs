using Bootis.Catalogo.Domain.AggregatesModel.ModeloRotuloAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.UnitTests.Fixtures.Produto;
using Bootis.Estoque.UnitTests.Fixtures.ProdutoDiluido;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Pharma.UnitTests.Common.Fakes.ModeloRotulo;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.UnitTests;

namespace Bootis.Estoque.UnitTests.Domain;

public class ProdutoTests : BaseTest
{
    private readonly Produto _produto =
        ProdutoFake.CreateProdutoMateriaPrimaFake();

    [Fact]
    public void Produto_DeveAtualizarProduto()
    {
        //Arrange
        var cmd = AtualizarBaseCommandFake.CreateCommandValidoProdutoAcabado();
        var subGrupo = SubGrupoFake.CriarSubGrupoValido();
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        //Action
        _produto.AtualizarProduto(cmd, subGrupo, fornecedor);

        //Assert
        Assert.Equal(subGrupo, _produto.SubGrupo);
        Assert.Equal(fornecedor, _produto.Fornecedor);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public void Produto_DeveAtualizarStatus(bool status)
    {
        //Action
        _produto.AtualizarStatus(status);

        //Assert
        Assert.Equal(status, _produto.Ativo);
    }

    [Theory]
    [InlineData(TipoClasseProdutoAbreviacao.Servico)]
    [InlineData(TipoClasseProdutoAbreviacao.UsoConsumo)]
    [InlineData(TipoClasseProdutoAbreviacao.CapsulaPronta)]
    [InlineData(TipoClasseProdutoAbreviacao.TipoCapsula)]
    [InlineData(TipoClasseProdutoAbreviacao.ProdutoAcabado)]
    [InlineData(TipoClasseProdutoAbreviacao.Embalagem)]
    [InlineData(TipoClasseProdutoAbreviacao.MateriaPrima)]
    public void Produto_DeveAtualizarClasseProduto(TipoClasseProdutoAbreviacao classeProduto)
    {
        //Action
        _produto.AtualizarClasseProduto(classeProduto);

        //Assert
        Assert.Equal(classeProduto, _produto.ClasseProdutoId);
    }

    [Fact]
    public void Produto_DeveAlterarValorCusto()
    {
        //Arrange
        decimal valorCusto = 10;

        //Action
        _produto.AlterarValorCusto(valorCusto);

        //Assert
        Assert.Equal(valorCusto, _produto.ValorCusto);
    }

    [Fact]
    public void Produto_DeveAtualizarValorVenda()
    {
        //Arrange 
        decimal valorVenda = 10;

        //Action
        _produto.AtualizarValorVenda(valorVenda);

        //Assert
        Assert.Equal(valorVenda, _produto.ValorVenda);
    }


    [Fact]
    public void Produto_DeveAtualizarProdutoEmbalagem()
    {
        //Arrange 
        var embalagemClassificacao = EmbalagemClassificacaoFake.CreateEmbalagemClassificacao_Ativo();
        var cmd = CadastrarEmbalagemCommandFake.CreateCommandValido();
        var produtosEmbalagem = new List<ProdutoEmbalagem>
            { ProdutoEmbalagemFake.CreateProdutoEmbalagemValido(_produto) };
        var capsulasTamanhoId = new List<Guid> { 1.ToGuid() };
        var modelosRotulos = new List<ModeloRotulo>() { ModeloRotuloFake.CreateModeloRotulo() };
        var formasFarmaceuticas = new List<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>() { FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo() };

        //Action
        _produto.AtualizarProdutoEmbalagem(embalagemClassificacao, cmd.Volume, cmd.EmbalagemAssociacao,
            produtosEmbalagem, cmd.NumeroCapsulaAssociacao, cmd.ModeloRotuloAssociacao, capsulasTamanhoId, formasFarmaceuticas, modelosRotulos);

        //Assert
        Assert.Equal(cmd.Volume, _produto.ProdutoEmbalagem.Volume);
        Assert.Equal(cmd.NumeroCapsulaAssociacao.First().QuantidadeCapsula,
            _produto.ProdutoEmbalagem.EmbalagemCapsulaTamanhoAssociacao.First().QuantidadeCapsula);
    }

    [Fact]
    public void Produto_DeveAtualizarProdutoTipoCapsula()
    {
        //Arrange
        var capsulaCor = CapsulaCorFake.CreateCapsulaCor();
        var tipoCapsula = TipoCapsulaFake.CreateTipoCapsula();
        var capsulaTamanho = CapsulaTamanhoFake.CreateCapsulaTamanho();

        //Action
        _produto.AtualizarProdutoTipoCapsula(capsulaCor, tipoCapsula, capsulaTamanho);

        //Assert
        Assert.Equal(capsulaTamanho, _produto.ProdutoTipoCapsula.CapsulaTamanho);
        Assert.Equal(tipoCapsula, _produto.ProdutoTipoCapsula.TipoCapsula);
        Assert.Equal(capsulaCor, _produto.ProdutoTipoCapsula.CapsulaCor);
    }

    [Fact(Skip = "Need to be fixed")]
    public void Produto_DeveAtualizarProdutoCapsulaPronta()
    {
        //Arrange 
        var capsulaTamanho = CapsulaTamanhoFake.CreateCapsulaTamanho();
        var produtosMateriaPrima = new List<ProdutoMateriaPrima>
            { ProdutoMateriaPrimaFake.CreateProdutoMateriaPrimaValido_IsExcipiente() };
        var cmd = CadastrarCapsulaProntaCommandFake.CreateCommandValido();

        //Action
        _produto.AtualizarProdutoCapsulaPronta(capsulaTamanho, produtosMateriaPrima, cmd.MateriaPrimaAssociacao);

        //Assert
        Assert.Equal(capsulaTamanho, _produto.ProdutoCapsulaPronta.CapsulaTamanho);
        Assert.NotEmpty(_produto.ProdutoCapsulaPronta.CapsulaProntaMateriaPrimaAssociacao);
    }

    [Fact]
    public void Produto_DeveAtualizarProdutoMateriaPrima()
    {
        //Arrange 
        var cmd = CadastrarMateriaPrimaCommandFake.CreateCommandValido();
        var cas = CasFake.CreateCas();
        var dcb = DcbFake.CreateDcb();
        var produtoExcipiente = ProdutoMateriaPrimaFake.CreateProdutoMateriaPrimaValido_IsPellets(_produto);

        //Action
        _produto.AtualizarProdutoMateriaPrima(cmd, cas, dcb, produtoExcipiente);

        //Assert
        Assert.Equal(cas, _produto.ProdutoMateriaPrima.Cas);
        Assert.Equal(dcb, _produto.ProdutoMateriaPrima.Dcb);
        Assert.Equal(produtoExcipiente, _produto.ProdutoMateriaPrima.ProdutoExcipienteEspecifico);
    }

    [Fact]
    public void Produto_DeveAdicionarNovoProdutoDiluido()
    {
        //Arrange 
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmdInterno = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido();
        var cmd = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido_DosagemDiferente();

        _produto.AdicionarProdutoDiluido(cmdInterno, formaFarmaceutica);

        //Action
        var produtoDiluido = _produto.AdicionarProdutoDiluido(cmd, formaFarmaceutica);

        //Assert
        Assert.Equal(_produto.ProdutoDiluido.Last(), produtoDiluido);
    }

    [Fact]
    public void Produto_DeveAtualizarProdutoDiluido()
    {
        //Arrange 
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmdInterno =
            CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido_DosagemDiferente();
        var cmd = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido();

        var produtoDiluido = _produto.AdicionarProdutoDiluido(cmd, formaFarmaceutica);

        //Action
        _produto.AtualizarProdutoDiluido(cmd, formaFarmaceutica, produtoDiluido);

        //Assert
        Assert.Equal(cmd.DosagemMaxima, produtoDiluido.DosagemMaxima);
        Assert.Equal(cmd.DosagemMinima, produtoDiluido.DosagemMinima);
        Assert.Equal(cmd.Diluicao, produtoDiluido.Diluicao);
    }

    [Fact]
    public void Produto_DeveValidarProdutoDiluido_ClasseProdutoIncorreta_ExecutadoComErro()
    {
        //Arrange 
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmd = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido_DosagemDiferente();

        _produto.AtualizarClasseProduto(TipoClasseProdutoAbreviacao.Embalagem);

        //Action
        var exception = Record.Exception(() => _produto.AdicionarProdutoDiluido(cmd, formaFarmaceutica));

        //Assert
        Assert.IsType<ValidationException>(exception);
    }

    [Fact]
    public void Produto_DeveValidarProdutoDiluido_ExecutadoComErro()
    {
        //Arrange 
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var cmdInterno =
            CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido_DosagemDiferente();
        var cmd = CadastrarProdutoDiluidoCommandFake.CreateCadastrarProdutoDiluidoCommandValido_DosagemIncompativel();

        _produto.AdicionarProdutoDiluido(cmdInterno, formaFarmaceutica);

        //Action
        var exception = Record.Exception(() => _produto.AdicionarProdutoDiluido(cmd, formaFarmaceutica));

        //Assert
        Assert.IsType<DomainException>(exception);
    }
}
